"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-decode-numeric-character-reference";
exports.ids = ["vendor-chunks/micromark-util-decode-numeric-character-reference"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeNumericCharacterReference: () => (/* binding */ decodeNumericCharacterReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/values.js\");\n\n\n/**\n * Turn the number (in string form as either hexa- or plain decimal) coming from\n * a numeric character reference into a character.\n *\n * Sort of like `String.fromCodePoint(Number.parseInt(value, base))`, but makes\n * non-characters and control characters safe.\n *\n * @param {string} value\n *   Value to decode.\n * @param {number} base\n *   Numeric base.\n * @returns {string}\n *   Character.\n */\nfunction decodeNumericCharacterReference(value, base) {\n  const code = Number.parseInt(value, base)\n\n  if (\n    // C0 except for HT, LF, FF, CR, space.\n    code < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ht ||\n    code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.vt ||\n    (code > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.cr && code < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space) ||\n    // Control character (DEL) of C0, and C1 controls.\n    (code > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde && code < 160) ||\n    // Lone high surrogates and low surrogates.\n    (code > 55_295 && code < 57_344) ||\n    // Noncharacters.\n    (code > 64_975 && code < 65_008) ||\n    /* eslint-disable no-bitwise */\n    (code & 65_535) === 65_535 ||\n    (code & 65_535) === 65_534 ||\n    /* eslint-enable no-bitwise */\n    // Out of range\n    code > 1_114_111\n  ) {\n    return micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.values.replacementCharacter\n  }\n\n  return String.fromCodePoint(code)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js\n");

/***/ })

};
;
#!/usr/bin/env python3
"""
FS - Flexible Soft AI Assistant Setup Script
Enhanced setup script for the LLaMA-powered chatbot system
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import json

def print_header():
    """Print setup header"""
    print("=" * 70)
    print("🚀 FS - Flexible Soft AI Assistant Setup")
    print("Enhanced LLaMA-powered chatbot with dynamic reasoning")
    print("=" * 70)
    print()

def check_python_version():
    """Check Python version"""
    print("📋 Checking Python version...")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required. Current version:", sys.version)
        sys.exit(1)
    print(f"✅ Python {sys.version.split()[0]} detected")
    print()

def check_node_version():
    """Check Node.js version"""
    print("📋 Checking Node.js version...")
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js {version} detected")
        else:
            print("❌ Node.js not found. Please install Node.js 18+")
            return False
    except FileNotFoundError:
        print("❌ Node.js not found. Please install Node.js 18+")
        return False
    print()
    return True

def setup_backend():
    """Setup backend environment"""
    print("🔧 Setting up backend...")
    
    # Create virtual environment
    backend_dir = Path("backend")
    venv_dir = backend_dir / "venv"
    
    if not venv_dir.exists():
        print("📦 Creating virtual environment...")
        subprocess.run([sys.executable, "-m", "venv", str(venv_dir)], check=True)
    
    # Determine activation script
    if os.name == 'nt':  # Windows
        activate_script = venv_dir / "Scripts" / "activate.bat"
        pip_path = venv_dir / "Scripts" / "pip.exe"
    else:  # Unix/Linux/macOS
        activate_script = venv_dir / "bin" / "activate"
        pip_path = venv_dir / "bin" / "pip"
    
    # Install requirements
    print("📦 Installing Python dependencies...")
    requirements_file = backend_dir / "requirements.txt"
    if requirements_file.exists():
        subprocess.run([str(pip_path), "install", "-r", str(requirements_file)], check=True)
    
    # Install additional AI dependencies
    print("🤖 Installing AI/ML dependencies...")
    ai_packages = [
        "torch>=2.0.0",
        "transformers>=4.30.0",
        "accelerate",
        "bitsandbytes",
    ]
    
    for package in ai_packages:
        try:
            subprocess.run([str(pip_path), "install", package], check=True)
            print(f"✅ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"⚠️ Failed to install {package} - you may need to install manually")
    
    # Setup environment file
    env_example = backend_dir / ".env.example"
    env_file = backend_dir / ".env"
    
    if env_example.exists() and not env_file.exists():
        print("📝 Creating .env file from template...")
        shutil.copy(env_example, env_file)
        print("⚠️ Please edit backend/.env with your configuration")
    
    print("✅ Backend setup complete!")
    print()

def setup_frontend():
    """Setup frontend environment"""
    print("🔧 Setting up frontend...")
    
    frontend_dir = Path("frontend")
    
    # Install npm dependencies
    print("📦 Installing Node.js dependencies...")
    subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
    
    # Setup environment file
    env_example = frontend_dir / ".env.example"
    env_local = frontend_dir / ".env.local"
    
    if env_example.exists() and not env_local.exists():
        print("📝 Creating .env.local file from template...")
        shutil.copy(env_example, env_local)
        print("⚠️ Please edit frontend/.env.local with your configuration")
    
    print("✅ Frontend setup complete!")
    print()

def create_directories():
    """Create necessary directories"""
    print("📁 Creating necessary directories...")
    
    directories = [
        "backend/logs",
        "backend/data",
        "backend/vector_store",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created {directory}")
    
    print()

def check_gpu_support():
    """Check GPU support"""
    print("🔍 Checking GPU support...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✅ CUDA GPU detected: {gpu_name} ({gpu_count} device(s))")
            print("💡 You can use GPU acceleration for faster model inference")
        else:
            print("⚠️ No CUDA GPU detected - will use CPU (slower)")
            print("💡 Consider using a GPU for better performance")
    except ImportError:
        print("⚠️ PyTorch not installed yet - GPU check will be available after setup")
    
    print()

def print_next_steps():
    """Print next steps"""
    print("🎉 Setup Complete!")
    print()
    print("📋 Next Steps:")
    print()
    print("1. 🔑 Configure your environment:")
    print("   - Edit backend/.env with your Hugging Face token")
    print("   - Edit frontend/.env.local with your API URL")
    print()
    print("2. 🚀 Start the application:")
    print("   Backend:")
    if os.name == 'nt':
        print("   cd backend && venv\\Scripts\\activate && python -m app.main_llama")
    else:
        print("   cd backend && source venv/bin/activate && python -m app.main_llama")
    print()
    print("   Frontend (in another terminal):")
    print("   cd frontend && npm run dev")
    print()
    print("3. 🌐 Access the application:")
    print("   - Frontend: http://localhost:3000")
    print("   - Backend API: http://localhost:8000")
    print("   - API Docs: http://localhost:8000/docs")
    print()
    print("4. 🤖 Model Configuration:")
    print("   - Get Hugging Face token: https://huggingface.co/settings/tokens")
    print("   - For LLaMA models, request access: https://huggingface.co/meta-llama")
    print("   - Alternative: Use DialoGPT models (no token required)")
    print()
    print("💡 Tips:")
    print("   - Use GPU for better performance")
    print("   - Start with DialoGPT-medium for testing")
    print("   - Upgrade to LLaMA-3-8b for production")
    print()
    print("🆘 Need help? Check the README.md or contact support")
    print("=" * 70)

def main():
    """Main setup function"""
    print_header()
    
    # Check prerequisites
    check_python_version()
    if not check_node_version():
        sys.exit(1)
    
    # Setup components
    create_directories()
    setup_backend()
    setup_frontend()
    check_gpu_support()
    
    # Print completion message
    print_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)

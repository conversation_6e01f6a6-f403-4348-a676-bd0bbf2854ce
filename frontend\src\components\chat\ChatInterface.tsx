'use client';

import { useState, useRef, useEffect } from 'react';
import { ChatMessage, ChatSession, Language } from '@/types';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { TypingIndicator } from './TypingIndicator';
import { sendMessage, sendMessageStream } from '@/lib/api';
import { generateId, generateSessionTitle, scrollToBottom } from '@/lib/utils';
import { DEFAULT_MESSAGES } from '@/lib/config';
import { toast } from 'react-hot-toast';

interface ChatInterfaceProps {
  session: ChatSession;
  onUpdateSession: (session: ChatSession) => void;
  language: Language;
}

export function ChatInterface({ session, onUpdateSession, language }: ChatInterfaceProps) {
  const [isTyping, setIsTyping] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [streamingMessage, setStreamingMessage] = useState<string>('');
  const [isStreaming, setIsStreaming] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      scrollToBottom(messagesEndRef.current.parentElement as HTMLElement);
    }
  }, [session.messages, isTyping]);

  // Add welcome message if session is empty
  useEffect(() => {
    if (session.messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: generateId(),
        role: 'assistant',
        content: DEFAULT_MESSAGES[language].welcome,
        timestamp: new Date(),
      };

      const updatedSession = {
        ...session,
        messages: [welcomeMessage],
        updated_at: new Date(),
      };

      onUpdateSession(updatedSession);
    }
  }, [session, language, onUpdateSession]);

  const handleSendMessage = async (content: string) => {
    if (!content.trim() || isTyping || isStreaming) return;

    setError(null);
    setStreamingMessage('');

    // Create user message
    const userMessage: ChatMessage = {
      id: generateId(),
      role: 'user',
      content: content.trim(),
      timestamp: new Date(),
    };

    // Update session with user message
    const updatedMessages = [...session.messages, userMessage];
    let updatedSession = {
      ...session,
      messages: updatedMessages,
      updated_at: new Date(),
    };

    // Update title if this is the first user message
    if (session.messages.filter(m => m.role === 'user').length === 0) {
      updatedSession.title = generateSessionTitle(updatedMessages);
    }

    onUpdateSession(updatedSession);
    setIsTyping(true);
    setIsStreaming(true);

    try {
      // Use streaming API for better user experience
      await sendMessageStream(
        {
          message: content,
          session_id: session.id,
          language,
        },
        // onChunk - called for each piece of the response
        (chunk: string) => {
          setStreamingMessage(prev => prev + chunk);
        },
        // onComplete - called when response is complete
        (fullResponse: string) => {
          setIsStreaming(false);
          setStreamingMessage('');

          // Create assistant message
          const assistantMessage: ChatMessage = {
            id: generateId(),
            role: 'assistant',
            content: fullResponse,
            timestamp: new Date(),
            metadata: {
              sources: ["FS - Flexible Soft AI Assistant (Enhanced LLaMA-Powered)"],
              language: language,
            },
          };

          // Update session with assistant message
          const finalMessages = [...updatedMessages, assistantMessage];
          const finalSession = {
            ...updatedSession,
            messages: finalMessages,
            updated_at: new Date(),
          };

          onUpdateSession(finalSession);
        },
        // onError - called if streaming fails
        (error: string) => {
          setIsStreaming(false);
          setStreamingMessage('');
          console.error('Streaming error:', error);
          setError(error || 'Failed to send message');

            content: DEFAULT_MESSAGES[language].error,
            timestamp: new Date(),
          };

          const errorSession = {
            ...updatedSession,
            messages: [...updatedMessages, errorMessage],
            updated_at: new Date(),
          };

          onUpdateSession(errorSession);
        }
      );

    } catch (error: any) {
      setIsStreaming(false);
      setStreamingMessage('');
      console.error('Error sending message:', error);
      setError(error.message || 'Failed to send message');

      // Create error message
      const errorMessage: ChatMessage = {
        id: generateId(),
        role: 'assistant',
        content: DEFAULT_MESSAGES[language].error,
        timestamp: new Date(),
      };

      const errorSession = {
        ...updatedSession,
        messages: [...updatedMessages, errorMessage],
        updated_at: new Date(),
      };

      onUpdateSession(errorSession);
      toast.error(error.message || 'Failed to send message');
    } finally {
      setIsTyping(false);
    }
  };

  const handleRetryMessage = (messageId: string) => {
    const messageIndex = session.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;

    const message = session.messages[messageIndex];
    if (message.role !== 'user') return;

    // Remove the user message and any subsequent messages
    const updatedMessages = session.messages.slice(0, messageIndex);
    const updatedSession = {
      ...session,
      messages: updatedMessages,
      updated_at: new Date(),
    };

    onUpdateSession(updatedSession);

    // Resend the message
    handleSendMessage(message.content);
  };

  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content).then(() => {
      toast.success('Message copied to clipboard');
    }).catch(() => {
      toast.error('Failed to copy message');
    });
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages Container */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto custom-scrollbar px-4 py-6"
      >
        <div className="max-w-4xl mx-auto">
          <MessageList
            messages={session.messages}
            language={language}
            onRetry={handleRetryMessage}
            onCopy={handleCopyMessage}
          />

          {/* Streaming Message */}
          {isStreaming && streamingMessage && (
            <div className="mb-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">FS</span>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="bg-white rounded-lg shadow-sm border p-4">
                    <div className="prose prose-sm max-w-none">
                      {streamingMessage}
                      <span className="inline-block w-2 h-4 bg-blue-500 animate-pulse ml-1"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Typing Indicator */}
          {isTyping && !isStreaming && (
            <div className="mb-4">
              <TypingIndicator />
            </div>
          )}
          
          {/* Error Message */}
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 text-sm">{error}</p>
              <button
                onClick={() => setError(null)}
                className="mt-2 text-red-600 hover:text-red-800 text-sm underline"
              >
                Dismiss
              </button>
            </div>
          )}
          
          {/* Scroll anchor */}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Message Input */}
      <div className="border-t border-slate-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <MessageInput
            onSendMessage={handleSendMessage}
            disabled={isTyping || isStreaming}
            language={language}
            placeholder={isStreaming ? 'FS is responding...' : DEFAULT_MESSAGES[language].placeholder}
          />
        </div>
      </div>
    </div>
  );
}

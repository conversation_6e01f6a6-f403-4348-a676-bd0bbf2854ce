# FS - Flexible Soft AI Assistant

🤖 **Enhanced LLaMA-powered intelligent conversation system with dynamic reasoning**

A next-generation AI assistant for Flexible Soft (Sofflex) that combines the power of LLaMA models with dynamic question categorization, streaming responses, and intelligent conversation management. Built with FastAPI (Python 3.13.7) and Next.js v14.2.32.

## ✨ Enhanced Features

### 🧠 **Dynamic Intelligence**
- **Smart Question Categorization**: Automatically classifies questions as company-specific, project-related, technical, or general
- **Dynamic Response Strategy**: Tailored responses based on question type and context
- **Natural Conversation Flow**: No more template-based responses - truly intelligent conversations
- **Context-Aware Responses**: Maintains conversation context across multiple exchanges

### 🚀 **Advanced AI Integration**
- **LLaMA-3 Support**: Enhanced with latest LLaMA-3-8b-chat-hf model
- **Multiple Model Fallback**: Automatic fallback to DialoGPT models if LLaMA unavailable
- **Streaming Responses**: Real-time response generation for better user experience
- **GPU Acceleration**: Optimized for both GPU and CPU deployment

### 🌍 **Multilingual Excellence**
- **Auto Language Detection**: Seamlessly detects Arabic and English
- **Contextual Responses**: Responds in user's preferred language
- **Cultural Awareness**: Understands regional business contexts

### 💼 **Business Intelligence**
- **Company Expertise**: Deep knowledge of Flexible Soft services and capabilities
- **Project Consultation**: Intelligent project recommendations and technical guidance
- **Industry Insights**: Connects general topics to technology solutions

## 📁 Project Structure

```
chatyy/
├── backend/                    # FastAPI backend
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI application
│   │   ├── config.py          # Configuration settings
│   │   ├── models/            # Pydantic models
│   │   ├── services/          # Business logic
│   │   ├── api/               # API endpoints
│   │   └── utils/             # Utility functions
│   ├── data/                  # Document storage
│   ├── vector_store/          # FAISS index storage
│   ├── requirements.txt
│   ├── Dockerfile
│   └── .env.example
├── frontend/                   # Next.js frontend
│   ├── src/
│   │   ├── app/               # App router
│   │   ├── components/        # React components
│   │   ├── lib/               # Utilities
│   │   └── types/             # TypeScript types
│   ├── public/                # Static assets
│   ├── package.json
│   ├── tailwind.config.js
│   ├── next.config.js
│   ├── Dockerfile
│   └── .env.example
├── docker-compose.yml         # Multi-container setup
└── README.md                  # This file
```

## 🛠 Enhanced Tech Stack

### 🔥 **AI/ML Core**
- **LLaMA-3-8b-chat-hf**: Latest Meta LLaMA model for superior conversation quality
- **DialoGPT**: Microsoft's conversational AI as intelligent fallback
- **Hugging Face Transformers**: Advanced model loading and inference
- **PyTorch**: GPU-accelerated deep learning framework
- **Dynamic Model Selection**: Automatic model fallback and optimization

### ⚡ **Backend Architecture**
- **Python 3.13.7** with FastAPI for high-performance APIs
- **Streaming Responses**: Real-time response generation with Server-Sent Events
- **Session Management**: Intelligent conversation context retention
- **Dynamic Categorization**: Pattern-matching based question classification
- **Multi-model Support**: Seamless switching between AI models

### 🎨 **Frontend Excellence**
- **Next.js 14.2.32** with TypeScript for type-safe development
- **Streaming UI**: Real-time message display with typing indicators
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Session Persistence**: Local storage with cross-tab synchronization
- **Multilingual Interface**: Arabic/English auto-switching

### 🔧 **Infrastructure**
- **FAISS** for vector similarity search (legacy document support)
- **SQLite** for lightweight data persistence
- **Environment-based Configuration**: Flexible deployment options
- **Docker Ready**: Complete containerization support

## 🚀 Enhanced Quick Start

### 📋 Prerequisites
- **Python 3.8+** (3.13.7 recommended)
- **Node.js 18+** with npm
- **Git** for version control
- **Hugging Face Account** (for LLaMA models)
- **GPU Support** (optional, for better performance)

### ⚡ Automated Setup

**Use our enhanced setup script for automatic configuration:**

```bash
# Clone the repository
git clone <repository-url>
cd chatyy

# Run automated setup
python setup_fs_assistant.py
```

The setup script will:
- ✅ Check system requirements
- 📦 Create virtual environments
- 🔧 Install all dependencies
- 📁 Create necessary directories
- 📝 Setup environment files
- 🔍 Check GPU support

### 🔧 Manual Setup

#### 1. **Backend Setup**
```bash
cd backend

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Setup environment
cp .env.example .env
# Edit .env with your Hugging Face token and preferences

# Start backend
python -m app.main_llama
```

#### 2. **Frontend Setup**
```bash
cd frontend

# Install dependencies
npm install

# Setup environment
cp .env.example .env.local
# Edit .env.local with your API URL

# Start frontend
npm run dev
```

#### 3. **Access the Application**
- 🌐 **Frontend**: http://localhost:3000
- 🔧 **Backend API**: http://localhost:8000
- 📚 **API Documentation**: http://localhost:8000/docs
- 📊 **Health Check**: http://localhost:8000/health

### 🐳 Docker Deployment

```bash
# Development environment
docker-compose up --build

# Run in background
docker-compose up -d

# Production environment
docker-compose -f docker-compose.prod.yml up -d

# Stop services
docker-compose down
```

### 🎯 First Time Setup

#### 1. **Configure AI Models**
```bash
# Get Hugging Face token
# Visit: https://huggingface.co/settings/tokens

# For LLaMA models, request access:
# Visit: https://huggingface.co/meta-llama/Llama-3-8b-chat-hf

# Edit backend/.env
HUGGINGFACE_TOKEN=your_token_here
MODEL_NAME=meta-llama/Llama-3-8b-chat-hf
```

#### 2. **Test the Enhanced Features**
- 🌐 Visit http://localhost:3000
- 💬 Try different question types:
  - **Company**: "What services does Flexible Soft offer?"
  - **Technical**: "How do I implement authentication in React?"
  - **General**: "What's the weather like?" (tests open AI capability)
  - **Arabic**: "مرحبا، كيف يمكنني مساعدتك؟"
- 🔄 Watch streaming responses in real-time
- 🧠 Notice intelligent categorization and dynamic responses

#### 3. **Performance Optimization**
- 🚀 **GPU Setup**: Install CUDA for faster inference
- 🔧 **Model Selection**: Start with DialoGPT for testing, upgrade to LLaMA for production
- 💾 **Memory Management**: Monitor RAM usage with larger models

## 📚 Enhanced API Endpoints

### 💬 **Chat Endpoints**
- `POST /api/chat` - Standard chat messages with intelligent responses
- `POST /api/chat/stream` - **NEW**: Real-time streaming responses
- `GET /api/chat/history/{session_id}` - Retrieve conversation history
- `DELETE /api/chat/history/{session_id}` - Clear conversation history

### 📄 **Document Management** (Legacy Support)
- `POST /api/upload` - Upload company documents
- `GET /api/documents` - List uploaded documents
- `DELETE /api/documents/{id}` - Delete document
- `POST /api/reindex` - Reindex vector store

### 🔧 **System Endpoints**
- `GET /health` - System health check
- `GET /api/info` - API and model information
- `GET /api/stats` - Usage statistics
- `POST /api/feedback` - Submit user feedback

## 🔧 Enhanced Configuration

### 🔑 **Key Environment Variables**

**Backend (.env)** - Enhanced Configuration
```env
# =============================================================================
# AI Model Configuration (MOST IMPORTANT)
# =============================================================================
HUGGINGFACE_TOKEN=your_huggingface_token_here
MODEL_NAME=meta-llama/Llama-3-8b-chat-hf
FALLBACK_MODELS=microsoft/DialoGPT-medium,microsoft/DialoGPT-large

# Model Parameters
MAX_TOKENS=300
TEMPERATURE=0.7
TOP_P=0.9
REPETITION_PENALTY=1.1

# =============================================================================
# API Configuration
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# =============================================================================
# Conversation Management
# =============================================================================
MAX_CONVERSATION_HISTORY=20
MAX_CONTEXT_MESSAGES=6
ENABLE_STREAMING=true
STREAMING_DELAY=0.05

# =============================================================================
# Language Support
# =============================================================================
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,ar
ENABLE_AUTO_LANGUAGE_DETECTION=true

# =============================================================================
# Company Branding
# =============================================================================
COMPANY_NAME=Flexible Soft
COMPANY_SHORT_NAME=Sofflex
COMPANY_DOMAIN=sofflex.com
COMPANY_WEBSITE=https://sofflex-website.dev.flexible-soft.com/
```

**Frontend (.env.local)**
```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_TIMEOUT=30000

# App Configuration
NEXT_PUBLIC_APP_NAME=Flexible Soft Assistant
NEXT_PUBLIC_APP_DESCRIPTION=AI-powered knowledge assistant for Flexible Soft
NEXT_PUBLIC_COMPANY_NAME=Flexible Soft
NEXT_PUBLIC_COMPANY_DOMAIN=sofflex.com

# Feature Flags
NEXT_PUBLIC_ENABLE_FILE_UPLOAD=true
NEXT_PUBLIC_ENABLE_FEEDBACK=true
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_MULTILINGUAL=true

# UI Configuration
NEXT_PUBLIC_DEFAULT_LANGUAGE=en
NEXT_PUBLIC_SUPPORTED_LANGUAGES=en,fr,ar
NEXT_PUBLIC_THEME=light
NEXT_PUBLIC_MAX_MESSAGE_LENGTH=2000
```

### Required Configuration Steps

1. **Hugging Face Token**:
   - Sign up at https://huggingface.co/
   - Generate an access token
   - Add it to `HUGGINGFACE_TOKEN` in backend .env

2. **Model Selection**:
   - Default: `meta-llama/Llama-2-7b-chat-hf` (requires HF token)
   - Alternative: Use a smaller model like `microsoft/DialoGPT-medium`
   - For production: Consider using OpenAI API or other hosted solutions

3. **Security**:
   - Change `SECRET_KEY` to a secure random string
   - Update `ALLOWED_ORIGINS` for production domains
   - Configure proper SSL certificates for production

4. **Storage**:
   - Ensure write permissions for `DOCUMENTS_PATH` and `VECTOR_STORE_PATH`
   - Consider using cloud storage for production deployments

## 🎨 Branding

The chatbot reflects Flexible Soft's professional yet friendly brand:
- Modern, clean interface design
- Company color scheme and typography
- Professional tone in responses
- Context-aware company references

## 🌐 Multilingual Support

Supports three languages:
- **English** (default)
- **French** 
- **Arabic**

## 📖 Usage

1. **Chat Interface**: Ask questions about company policies, services, or general programming topics
2. **Document Upload**: Admins can upload company documents to expand the knowledge base
3. **Admin Dashboard**: Manage uploaded documents and monitor chat analytics

## 🔒 Security

- Environment-based configuration
- Input validation and sanitization
- Rate limiting on API endpoints
- Secure file upload handling

## 🚀 Deployment

### Production Deployment

1. **Using Docker Compose**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

2. **Manual Deployment**
- Deploy backend to your Python hosting service
- Deploy frontend to Vercel/Netlify
- Configure environment variables
- Set up reverse proxy (nginx)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

Copyright © 2024 Flexible Soft / Sofflex. All rights reserved.

## 🆘 Support

For support, contact the Flexible Soft development team or create an issue in the repository.

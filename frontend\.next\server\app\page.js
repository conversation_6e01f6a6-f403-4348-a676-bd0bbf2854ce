(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},152:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),n(5480),n(3525),n(5866);var a=n(3191),r=n(8716),i=n(7922),o=n.n(i),s=n(5231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);n.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,5480)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Chatyy\\frontend\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(n.bind(n,3525)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Chatyy\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,5866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment-projects\\Chatyy\\frontend\\src\\app\\page.tsx"],u="/page",m={require:n,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9521:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2994,23)),Promise.resolve().then(n.t.bind(n,6114,23)),Promise.resolve().then(n.t.bind(n,9727,23)),Promise.resolve().then(n.t.bind(n,9671,23)),Promise.resolve().then(n.t.bind(n,1868,23)),Promise.resolve().then(n.t.bind(n,4759,23))},6926:(e,t,n)=>{Promise.resolve().then(n.bind(n,381))},5157:(e,t,n)=>{Promise.resolve().then(n.bind(n,3143))},3143:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>ek});var a=n(326),r=n(7577),i=n(2695),o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,t)=>{let n=(0,r.forwardRef)(({color:n="currentColor",size:a=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:d="",children:c,...u},m)=>(0,r.createElement)("svg",{ref:m,...o,width:a,height:a,stroke:n,strokeWidth:l?24*Number(i)/Number(a):i,className:["lucide",`lucide-${s(e)}`,d].join(" "),...u},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return n.displayName=`${e}`,n},d=l("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),c=l("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),u=l("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),m=l("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),h=l("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]),f=l("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),g=l("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),x=l("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]);function p(e){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function b(e,t){if(t.length<e)throw TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function v(e){b(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||"object"===p(e)&&"[object Date]"===t?new Date(e.getTime()):"number"==typeof e||"[object Number]"===t?new Date(e):(("string"==typeof e||"[object String]"===t)&&"undefined"!=typeof console&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(Error().stack)),new Date(NaN))}function y(e){b(1,arguments);var t=v(e);return t.setHours(0,0,0,0),t}function w(e,t){b(2,arguments);var n=y(e),a=y(t);return n.getTime()===a.getTime()}function j(e){if(null===e||!0===e||!1===e)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}function N(e){b(1,arguments);var t=v(e),n=t.getUTCDay();return t.setUTCDate(t.getUTCDate()-((n<1?7:0)+n-1)),t.setUTCHours(0,0,0,0),t}function k(e){b(1,arguments);var t=v(e),n=t.getUTCFullYear(),a=new Date(0);a.setUTCFullYear(n+1,0,4),a.setUTCHours(0,0,0,0);var r=N(a),i=new Date(0);i.setUTCFullYear(n,0,4),i.setUTCHours(0,0,0,0);var o=N(i);return t.getTime()>=r.getTime()?n+1:t.getTime()>=o.getTime()?n:n-1}var C={};function S(e,t){b(1,arguments);var n,a,r,i,o,s,l,d,c=j(null!==(n=null!==(a=null!==(r=null!==(i=null==t?void 0:t.weekStartsOn)&&void 0!==i?i:null==t?void 0:null===(o=t.locale)||void 0===o?void 0:null===(s=o.options)||void 0===s?void 0:s.weekStartsOn)&&void 0!==r?r:C.weekStartsOn)&&void 0!==a?a:null===(l=C.locale)||void 0===l?void 0:null===(d=l.options)||void 0===d?void 0:d.weekStartsOn)&&void 0!==n?n:0);if(!(c>=0&&c<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");var u=v(e),m=u.getUTCDay();return u.setUTCDate(u.getUTCDate()-((m<c?7:0)+m-c)),u.setUTCHours(0,0,0,0),u}function T(e,t){b(1,arguments);var n,a,r,i,o,s,l,d,c=v(e),u=c.getUTCFullYear(),m=j(null!==(n=null!==(a=null!==(r=null!==(i=null==t?void 0:t.firstWeekContainsDate)&&void 0!==i?i:null==t?void 0:null===(o=t.locale)||void 0===o?void 0:null===(s=o.options)||void 0===s?void 0:s.firstWeekContainsDate)&&void 0!==r?r:C.firstWeekContainsDate)&&void 0!==a?a:null===(l=C.locale)||void 0===l?void 0:null===(d=l.options)||void 0===d?void 0:d.firstWeekContainsDate)&&void 0!==n?n:1);if(!(m>=1&&m<=7))throw RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var h=new Date(0);h.setUTCFullYear(u+1,0,m),h.setUTCHours(0,0,0,0);var f=S(h,t),g=new Date(0);g.setUTCFullYear(u,0,m),g.setUTCHours(0,0,0,0);var x=S(g,t);return c.getTime()>=f.getTime()?u+1:c.getTime()>=x.getTime()?u:u-1}function M(e,t){for(var n=Math.abs(e).toString();n.length<t;)n="0"+n;return(e<0?"-":"")+n}let D={y:function(e,t){var n=e.getUTCFullYear(),a=n>0?n:1-n;return M("yy"===t?a%100:a,t.length)},M:function(e,t){var n=e.getUTCMonth();return"M"===t?String(n+1):M(n+1,2)},d:function(e,t){return M(e.getUTCDate(),t.length)},h:function(e,t){return M(e.getUTCHours()%12||12,t.length)},H:function(e,t){return M(e.getUTCHours(),t.length)},m:function(e,t){return M(e.getUTCMinutes(),t.length)},s:function(e,t){return M(e.getUTCSeconds(),t.length)},S:function(e,t){var n=t.length;return M(Math.floor(e.getUTCMilliseconds()*Math.pow(10,n-3)),t.length)}};var P={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"};function U(e,t){var n=e>0?"-":"+",a=Math.abs(e),r=Math.floor(a/60),i=a%60;return 0===i?n+String(r):n+String(r)+(t||"")+M(i,2)}function F(e,t){return e%60==0?(e>0?"-":"+")+M(Math.abs(e)/60,2):E(e,t)}function E(e,t){var n=Math.abs(e);return(e>0?"-":"+")+M(Math.floor(n/60),2)+(t||"")+M(n%60,2)}let A={G:function(e,t,n){var a=e.getUTCFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(a,{width:"abbreviated"});case"GGGGG":return n.era(a,{width:"narrow"});default:return n.era(a,{width:"wide"})}},y:function(e,t,n){if("yo"===t){var a=e.getUTCFullYear();return n.ordinalNumber(a>0?a:1-a,{unit:"year"})}return D.y(e,t)},Y:function(e,t,n,a){var r=T(e,a),i=r>0?r:1-r;return"YY"===t?M(i%100,2):"Yo"===t?n.ordinalNumber(i,{unit:"year"}):M(i,t.length)},R:function(e,t){return M(k(e),t.length)},u:function(e,t){return M(e.getUTCFullYear(),t.length)},Q:function(e,t,n){var a=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"Q":return String(a);case"QQ":return M(a,2);case"Qo":return n.ordinalNumber(a,{unit:"quarter"});case"QQQ":return n.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(a,{width:"narrow",context:"formatting"});default:return n.quarter(a,{width:"wide",context:"formatting"})}},q:function(e,t,n){var a=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"q":return String(a);case"qq":return M(a,2);case"qo":return n.ordinalNumber(a,{unit:"quarter"});case"qqq":return n.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(a,{width:"narrow",context:"standalone"});default:return n.quarter(a,{width:"wide",context:"standalone"})}},M:function(e,t,n){var a=e.getUTCMonth();switch(t){case"M":case"MM":return D.M(e,t);case"Mo":return n.ordinalNumber(a+1,{unit:"month"});case"MMM":return n.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(a,{width:"narrow",context:"formatting"});default:return n.month(a,{width:"wide",context:"formatting"})}},L:function(e,t,n){var a=e.getUTCMonth();switch(t){case"L":return String(a+1);case"LL":return M(a+1,2);case"Lo":return n.ordinalNumber(a+1,{unit:"month"});case"LLL":return n.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(a,{width:"narrow",context:"standalone"});default:return n.month(a,{width:"wide",context:"standalone"})}},w:function(e,t,n,a){var r=function(e,t){b(1,arguments);var n=v(e);return Math.round((S(n,t).getTime()-(function(e,t){b(1,arguments);var n,a,r,i,o,s,l,d,c=j(null!==(n=null!==(a=null!==(r=null!==(i=null==t?void 0:t.firstWeekContainsDate)&&void 0!==i?i:null==t?void 0:null===(o=t.locale)||void 0===o?void 0:null===(s=o.options)||void 0===s?void 0:s.firstWeekContainsDate)&&void 0!==r?r:C.firstWeekContainsDate)&&void 0!==a?a:null===(l=C.locale)||void 0===l?void 0:null===(d=l.options)||void 0===d?void 0:d.firstWeekContainsDate)&&void 0!==n?n:1),u=T(e,t),m=new Date(0);return m.setUTCFullYear(u,0,c),m.setUTCHours(0,0,0,0),S(m,t)})(n,t).getTime())/6048e5)+1}(e,a);return"wo"===t?n.ordinalNumber(r,{unit:"week"}):M(r,t.length)},I:function(e,t,n){var a=function(e){b(1,arguments);var t=v(e);return Math.round((N(t).getTime()-(function(e){b(1,arguments);var t=k(e),n=new Date(0);return n.setUTCFullYear(t,0,4),n.setUTCHours(0,0,0,0),N(n)})(t).getTime())/6048e5)+1}(e);return"Io"===t?n.ordinalNumber(a,{unit:"week"}):M(a,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getUTCDate(),{unit:"date"}):D.d(e,t)},D:function(e,t,n){var a=function(e){b(1,arguments);var t=v(e),n=t.getTime();return t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0),Math.floor((n-t.getTime())/864e5)+1}(e);return"Do"===t?n.ordinalNumber(a,{unit:"dayOfYear"}):M(a,t.length)},E:function(e,t,n){var a=e.getUTCDay();switch(t){case"E":case"EE":case"EEE":return n.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},e:function(e,t,n,a){var r=e.getUTCDay(),i=(r-a.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return M(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},c:function(e,t,n,a){var r=e.getUTCDay(),i=(r-a.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return M(i,t.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(r,{width:"narrow",context:"standalone"});case"cccccc":return n.day(r,{width:"short",context:"standalone"});default:return n.day(r,{width:"wide",context:"standalone"})}},i:function(e,t,n){var a=e.getUTCDay(),r=0===a?7:a;switch(t){case"i":return String(r);case"ii":return M(r,t.length);case"io":return n.ordinalNumber(r,{unit:"day"});case"iii":return n.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},a:function(e,t,n){var a=e.getUTCHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(e,t,n){var a,r=e.getUTCHours();switch(a=12===r?P.noon:0===r?P.midnight:r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(e,t,n){var a,r=e.getUTCHours();switch(a=r>=17?P.evening:r>=12?P.afternoon:r>=4?P.morning:P.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){var a=e.getUTCHours()%12;return 0===a&&(a=12),n.ordinalNumber(a,{unit:"hour"})}return D.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getUTCHours(),{unit:"hour"}):D.H(e,t)},K:function(e,t,n){var a=e.getUTCHours()%12;return"Ko"===t?n.ordinalNumber(a,{unit:"hour"}):M(a,t.length)},k:function(e,t,n){var a=e.getUTCHours();return(0===a&&(a=24),"ko"===t)?n.ordinalNumber(a,{unit:"hour"}):M(a,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):D.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):D.s(e,t)},S:function(e,t){return D.S(e,t)},X:function(e,t,n,a){var r=(a._originalDate||e).getTimezoneOffset();if(0===r)return"Z";switch(t){case"X":return F(r);case"XXXX":case"XX":return E(r);default:return E(r,":")}},x:function(e,t,n,a){var r=(a._originalDate||e).getTimezoneOffset();switch(t){case"x":return F(r);case"xxxx":case"xx":return E(r);default:return E(r,":")}},O:function(e,t,n,a){var r=(a._originalDate||e).getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+U(r,":");default:return"GMT"+E(r,":")}},z:function(e,t,n,a){var r=(a._originalDate||e).getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+U(r,":");default:return"GMT"+E(r,":")}},t:function(e,t,n,a){return M(Math.floor((a._originalDate||e).getTime()/1e3),t.length)},T:function(e,t,n,a){return M((a._originalDate||e).getTime(),t.length)}};var q=function(e,t){switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},W=function(e,t){switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}};let z={p:W,P:function(e,t){var n,a=e.match(/(P+)(p+)?/)||[],r=a[1],i=a[2];if(!i)return q(e,t);switch(r){case"P":n=t.dateTime({width:"short"});break;case"PP":n=t.dateTime({width:"medium"});break;case"PPP":n=t.dateTime({width:"long"});break;default:n=t.dateTime({width:"full"})}return n.replace("{{date}}",q(r,t)).replace("{{time}}",W(i,t))}};var Y=["D","DD"],H=["YY","YYYY"];function O(e,t,n){if("YYYY"===e)throw RangeError("Use `yyyy` instead of `YYYY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("YY"===e)throw RangeError("Use `yy` instead of `YY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("D"===e)throw RangeError("Use `d` instead of `D` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("DD"===e)throw RangeError("Use `dd` instead of `DD` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var L={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function I(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}var R={date:I({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:I({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:I({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},G={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function _(e){return function(t,n){var a;if("formatting"===(null!=n&&n.context?String(n.context):"standalone")&&e.formattingValues){var r=e.defaultFormattingWidth||e.defaultWidth,i=null!=n&&n.width?String(n.width):r;a=e.formattingValues[i]||e.formattingValues[r]}else{var o=e.defaultWidth,s=null!=n&&n.width?String(n.width):e.defaultWidth;a=e.values[s]||e.values[o]}return a[e.argumentCallback?e.argumentCallback(t):t]}}function Q(e){return function(t){var n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=a.width,i=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],o=t.match(i);if(!o)return null;var s=o[0],l=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(l)?function(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return n}(l,function(e){return e.test(s)}):function(e,t){for(var n in e)if(e.hasOwnProperty(n)&&t(e[n]))return n}(l,function(e){return e.test(s)});return n=e.valueCallback?e.valueCallback(d):d,{value:n=a.valueCallback?a.valueCallback(n):n,rest:t.slice(s.length)}}}let $={code:"en-US",formatDistance:function(e,t,n){var a,r=L[e];return(a="string"==typeof r?r:1===t?r.one:r.other.replace("{{count}}",t.toString()),null!=n&&n.addSuffix)?n.comparison&&n.comparison>0?"in "+a:a+" ago":a},formatLong:R,formatRelative:function(e,t,n,a){return G[e]},localize:{ordinalNumber:function(e,t){var n=Number(e),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:_({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:_({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:_({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:_({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:_({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=t.match(e.matchPattern);if(!a)return null;var r=a[0],i=t.match(e.parsePattern);if(!i)return null;var o=e.valueCallback?e.valueCallback(i[0]):i[0];return{value:o=n.valueCallback?n.valueCallback(o):o,rest:t.slice(r.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}}),era:Q({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:Q({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:Q({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Q({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};var B=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,X=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,V=/^'([^]*?)'?$/,J=/''/g,Z=/[a-zA-Z]/;function K(e,t,n){b(2,arguments);var a,r,i,o,s,l,d,c,u,m,h,f,g,x,y,w,N,k,S,T=String(t),M=null!==(r=null!==(i=null==n?void 0:n.locale)&&void 0!==i?i:C.locale)&&void 0!==r?r:$,D=j(null!==(o=null!==(s=null!==(l=null!==(d=null==n?void 0:n.firstWeekContainsDate)&&void 0!==d?d:null==n?void 0:null===(c=n.locale)||void 0===c?void 0:null===(u=c.options)||void 0===u?void 0:u.firstWeekContainsDate)&&void 0!==l?l:C.firstWeekContainsDate)&&void 0!==s?s:null===(m=C.locale)||void 0===m?void 0:null===(h=m.options)||void 0===h?void 0:h.firstWeekContainsDate)&&void 0!==o?o:1);if(!(D>=1&&D<=7))throw RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var P=j(null!==(f=null!==(g=null!==(x=null!==(y=null==n?void 0:n.weekStartsOn)&&void 0!==y?y:null==n?void 0:null===(w=n.locale)||void 0===w?void 0:null===(N=w.options)||void 0===N?void 0:N.weekStartsOn)&&void 0!==x?x:C.weekStartsOn)&&void 0!==g?g:null===(k=C.locale)||void 0===k?void 0:null===(S=k.options)||void 0===S?void 0:S.weekStartsOn)&&void 0!==f?f:0);if(!(P>=0&&P<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!M.localize)throw RangeError("locale must contain localize property");if(!M.formatLong)throw RangeError("locale must contain formatLong property");var U=v(e);if(!function(e){return b(1,arguments),(!!function(e){return b(1,arguments),e instanceof Date||"object"===p(e)&&"[object Date]"===Object.prototype.toString.call(e)}(e)||"number"==typeof e)&&!isNaN(Number(v(e)))}(U))throw RangeError("Invalid time value");var F=((a=new Date(Date.UTC(U.getFullYear(),U.getMonth(),U.getDate(),U.getHours(),U.getMinutes(),U.getSeconds(),U.getMilliseconds()))).setUTCFullYear(U.getFullYear()),U.getTime()-a.getTime()),E=function(e,t){return b(2,arguments),function(e,t){return b(2,arguments),new Date(v(e).getTime()+j(t))}(e,-j(t))}(U,F),q={firstWeekContainsDate:D,weekStartsOn:P,locale:M,_originalDate:U};return T.match(X).map(function(e){var t=e[0];return"p"===t||"P"===t?(0,z[t])(e,M.formatLong):e}).join("").match(B).map(function(a){if("''"===a)return"'";var r,i=a[0];if("'"===i)return(r=a.match(V))?r[1].replace(J,"'"):a;var o=A[i];if(o)return null!=n&&n.useAdditionalWeekYearTokens||-1===H.indexOf(a)||O(a,t,String(e)),null!=n&&n.useAdditionalDayOfYearTokens||-1===Y.indexOf(a)||O(a,t,String(e)),o(E,a,M.localize,q);if(i.match(Z))throw RangeError("Format string contains an unescaped latin alphabet character `"+i+"`");return a}).join("")}let ee=require("crypto");var et=n.n(ee);let en={randomUUID:et().randomUUID},ea=new Uint8Array(256),er=ea.length,ei=[];for(let e=0;e<256;++e)ei.push((e+256).toString(16).slice(1));let eo=function(e,t,n){if(en.randomUUID&&!t&&!e)return en.randomUUID();let a=(e=e||{}).random||(e.rng||function(){return er>ea.length-16&&(et().randomFillSync(ea),er=0),ea.slice(er,er+=16)})();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){n=n||0;for(let e=0;e<16;++e)t[n+e]=a[e];return t}return function(e,t=0){return ei[e[t+0]]+ei[e[t+1]]+ei[e[t+2]]+ei[e[t+3]]+"-"+ei[e[t+4]]+ei[e[t+5]]+"-"+ei[e[t+6]]+ei[e[t+7]]+"-"+ei[e[t+8]]+ei[e[t+9]]+"-"+ei[e[t+10]]+ei[e[t+11]]+ei[e[t+12]]+ei[e[t+13]]+ei[e[t+14]]+ei[e[t+15]]}(a)};function es(e){let t="string"==typeof e?new Date(e):e;return!function(e){return b(1,arguments),w(e,Date.now())}(t)?!function(e){return b(1,arguments),w(e,function(e,t){return b(2,arguments),function(e,t){b(2,arguments);var n=v(e),a=j(t);return isNaN(a)?new Date(NaN):(a&&n.setDate(n.getDate()+a),n)}(e,-j(t))}(Date.now(),1))}(t)?K(t,"MMM dd, HH:mm"):"Yesterday "+K(t,"HH:mm"):K(t,"HH:mm")}function el(e,t){return e.length<=t?e:e.slice(0,t)+"..."}function ed(e){let t=e.find(e=>"user"===e.role);return t&&el(t.content,50)||"New Chat"}let ec={apiUrl:"http://localhost:8000",appName:"Flexible Soft Assistant",companyName:"Flexible Soft",maxMessageLength:parseInt("2000"),maxFileSize:parseInt("10485760"),allowedFileTypes:"pdf,docx,txt,md".split(","),supportedLanguages:"en,fr,ar".split(","),defaultLanguage:"en",enableFileUpload:!0,enableFeedback:!0,enableMultilingual:!0},eu={chatSessions:"flexible-soft-chat-sessions",currentSession:"flexible-soft-current-session"},em={en:{welcome:`Hello! I'm the Flexible Soft AI Assistant. I can help you with:
    
• Questions about Flexible Soft services and policies
• Technical guidance on software development
• Information about our technologies and expertise
• General programming and AI questions

How can I assist you today?`,placeholder:"Ask me anything about Flexible Soft or technical topics...",thinking:"Thinking...",error:"Sorry, I encountered an error. Please try again.",uploadSuccess:"Document uploaded successfully!",uploadError:"Failed to upload document. Please try again."},fr:{welcome:`Bonjour ! Je suis l'assistant IA de Flexible Soft. Je peux vous aider avec :

• Questions sur les services et politiques de Flexible Soft
• Conseils techniques sur le d\xe9veloppement logiciel
• Informations sur nos technologies et expertise
• Questions g\xe9n\xe9rales sur la programmation et l'IA

Comment puis-je vous aider aujourd'hui ?`,placeholder:"Posez-moi des questions sur Flexible Soft ou des sujets techniques...",thinking:"R\xe9flexion...",error:"D\xe9sol\xe9, j'ai rencontr\xe9 une erreur. Veuillez r\xe9essayer.",uploadSuccess:"Document t\xe9l\xe9charg\xe9 avec succ\xe8s !",uploadError:"\xc9chec du t\xe9l\xe9chargement du document. Veuillez r\xe9essayer."},ar:{welcome:`مرحباً! أنا مساعد الذكاء الاصطناعي لشركة Flexible Soft. يمكنني مساعدتك في:

• أسئلة حول خدمات وسياسات Flexible Soft
• إرشادات تقنية حول تطوير البرمجيات
• معلومات حول تقنياتنا وخبراتنا
• أسئلة عامة حول البرمجة والذكاء الاصطناعي

كيف يمكنني مساعدتك اليوم؟`,placeholder:"اسألني أي شيء عن Flexible Soft أو المواضيع التقنية...",thinking:"أفكر...",error:"عذراً، واجهت خطأ. يرجى المحاولة مرة أخرى.",uploadSuccess:"تم رفع المستند بنجاح!",uploadError:"فشل في رفع المستند. يرجى المحاولة مرة أخرى."}},eh={name:"Flexible Soft",description:"A leading software, web, and AI development company",technologies:["React","Next.js","TypeScript","Python","AI/ML"]};function ef({isOpen:e,onClose:t,sessions:n,currentSession:i,onNewSession:o,onSelectSession:s,onDeleteSession:l,onClearAll:p,language:b}){let[v,y]=(0,r.useState)(""),[w,j]=(0,r.useState)(!1),[N,k]=(0,r.useState)(null),C=n.filter(e=>{let t=e.title||ed(e.messages),n=e.messages.map(e=>e.content).join(" "),a=v.toLowerCase();return t.toLowerCase().includes(a)||n.toLowerCase().includes(a)}),S=e=>{l(e),k(null)},T=(0,a.jsxs)("div",{className:"flex flex-col h-full bg-white border-r border-slate-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-slate-100",children:[a.jsx("h2",{className:"text-lg font-semibold text-slate-900",children:"en"===b?"Chat History":"fr"===b?"Historique des discussions":"تاريخ المحادثات"}),a.jsx("button",{onClick:t,className:"p-1 text-slate-400 hover:text-slate-600 lg:hidden",children:a.jsx(d,{className:"w-5 h-5"})})]}),a.jsx("div",{className:"p-4",children:(0,a.jsxs)("button",{onClick:()=>{o(),t()},className:"w-full flex items-center gap-3 p-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200",children:[a.jsx(c,{className:"w-5 h-5"}),a.jsx("span",{className:"font-medium",children:"en"===b?"New Chat":"fr"===b?"Nouvelle discussion":"محادثة جديدة"})]})}),a.jsx("div",{className:"px-4 pb-4",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(u,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),a.jsx("input",{type:"text",placeholder:"en"===b?"Search conversations...":"fr"===b?"Rechercher des conversations...":"البحث في المحادثات...",value:v,onChange:e=>y(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})}),a.jsx("div",{className:"flex-1 overflow-y-auto custom-scrollbar px-4",children:0===C.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(m,{className:"w-12 h-12 text-slate-300 mx-auto mb-3"}),a.jsx("p",{className:"text-slate-500 text-sm",children:v?"en"===b?"No conversations found":"fr"===b?"Aucune conversation trouv\xe9e":"لم يتم العثور على محادثات":"en"===b?"No conversations yet":"fr"===b?"Aucune conversation pour le moment":"لا توجد محادثات بعد"})]}):a.jsx("div",{className:"space-y-2",children:C.map(e=>{let n=e.title||ed(e.messages),r=e.messages[e.messages.length-1],o=i?.id===e.id;return a.jsx("div",{className:`group relative p-3 rounded-lg cursor-pointer transition-all duration-200 ${o?"bg-primary-50 border border-primary-200":"hover:bg-slate-50 border border-transparent"}`,onClick:()=>{s(e),t()},children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("h3",{className:`text-sm font-medium truncate ${o?"text-primary-900":"text-slate-900"}`,children:el(n,40)}),r&&a.jsx("p",{className:`text-xs mt-1 truncate ${o?"text-primary-600":"text-slate-500"}`,children:el(r.content,50)}),a.jsx("p",{className:`text-xs mt-1 ${o?"text-primary-500":"text-slate-400"}`,children:es(e.updated_at)})]}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("button",{onClick:t=>{t.stopPropagation(),k(N===e.id?null:e.id)},className:"p-1 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:a.jsx(h,{className:"w-4 h-4"})}),N===e.id&&a.jsx("div",{className:"absolute right-0 top-full mt-1 w-32 bg-white rounded-lg shadow-lg border border-slate-200 py-1 z-50",children:(0,a.jsxs)("button",{onClick:t=>{t.stopPropagation(),S(e.id)},className:"w-full flex items-center gap-2 px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",children:[a.jsx(f,{className:"w-4 h-4"}),"en"===b?"Delete":"fr"===b?"Supprimer":"حذف"]})})]})]})},e.id)})})}),(0,a.jsxs)("div",{className:"p-4 border-t border-slate-100 space-y-2",children:[ec.enableFileUpload&&(0,a.jsxs)("button",{onClick:()=>{t()},className:"w-full flex items-center gap-3 p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200",children:[a.jsx(g,{className:"w-4 h-4"}),a.jsx("span",{className:"text-sm",children:"en"===b?"Upload Documents":"fr"===b?"T\xe9l\xe9charger des documents":"رفع المستندات"})]}),(0,a.jsxs)("button",{onClick:()=>{t()},className:"w-full flex items-center gap-3 p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200",children:[a.jsx(x,{className:"w-4 h-4"}),a.jsx("span",{className:"text-sm",children:"en"===b?"Manage Documents":"fr"===b?"G\xe9rer les documents":"إدارة المستندات"})]}),n.length>0&&(0,a.jsxs)("button",{onClick:()=>{w?(p(),j(!1)):j(!0)},className:`w-full flex items-center gap-3 p-2 rounded-lg transition-colors duration-200 ${w?"text-red-700 bg-red-50 hover:bg-red-100":"text-slate-600 hover:text-red-600 hover:bg-red-50"}`,children:[a.jsx(f,{className:"w-4 h-4"}),a.jsx("span",{className:"text-sm",children:w?"en"===b?"Confirm Clear All":"fr"===b?"Confirmer tout effacer":"تأكيد مسح الكل":"en"===b?"Clear All Chats":"fr"===b?"Effacer toutes les discussions":"مسح جميع المحادثات"})]})]})]});return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"hidden lg:block w-80 h-full",children:T}),a.jsx("div",{className:`lg:hidden fixed inset-y-0 left-0 z-50 w-80 transform transition-transform duration-300 ease-in-out ${e?"translate-x-0":"-translate-x-full"}`,children:T}),N&&a.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>k(null)})]})}let eg=l("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),ex=l("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),ep=l("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),eb=l("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);function ev({onToggleSidebar:e,currentSession:t,language:n,onLanguageChange:i}){let[o,s]=(0,r.useState)(!1),[l,d]=(0,r.useState)(!1);return(0,a.jsxs)("header",{className:"bg-white/80 backdrop-blur-sm border-b border-slate-200 px-4 py-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx("button",{onClick:e,className:"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors duration-200 lg:hidden",children:a.jsx(eg,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-sm",children:"FS"})}),(0,a.jsxs)("div",{className:"hidden sm:block",children:[a.jsx("h1",{className:"text-lg font-semibold text-slate-900",children:ec.appName}),a.jsx("p",{className:"text-xs text-slate-500",children:eh.description})]})]})]}),t&&a.jsx("div",{className:"hidden md:block flex-1 max-w-md mx-4",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h2",{className:"text-sm font-medium text-slate-900 truncate",children:t.title||ed(t.messages)}),(0,a.jsxs)("p",{className:"text-xs text-slate-500",children:[t.messages.length," messages"]})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[t&&t.messages.length>0&&a.jsx("button",{onClick:()=>{t&&t.messages.length>0&&function(e,t){let n=new Blob([e.map(e=>`[${es(e.timestamp)}] ${e.role.toUpperCase()}: ${e.content}`).join("\n\n")],{type:"text/plain"}),a=URL.createObjectURL(n),r=document.createElement("a");r.href=a,r.download=`chat-history-${t}-${K(new Date,"yyyy-MM-dd")}.txt`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(a)}(t.messages,t.id)},className:"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors duration-200",title:"Export chat history",children:a.jsx(ex,{className:"w-5 h-5"})}),ec.enableMultilingual&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>s(!o),className:"flex items-center gap-2 p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors duration-200",title:"Change language",children:[a.jsx(ep,{className:"w-5 h-5"}),a.jsx("span",{className:"hidden sm:inline text-sm",children:{en:"English",fr:"Fran\xe7ais",ar:"العربية"}[n]||n})]}),o&&a.jsx("div",{className:"absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200 py-2 z-50",children:[{code:"en",name:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"fr",name:"Fran\xe7ais",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"ar",name:"العربية",flag:"\uD83C\uDDF9\uD83C\uDDF3"}].map(e=>(0,a.jsxs)("button",{onClick:()=>{i(e.code),s(!1)},className:`w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-slate-50 transition-colors duration-200 ${n===e.code?"bg-primary-50 text-primary-700":"text-slate-700"}`,children:[a.jsx("span",{className:"text-lg",children:e.flag}),a.jsx("span",{className:"font-medium",children:e.name}),n===e.code&&a.jsx("span",{className:"ml-auto text-primary-600",children:"✓"})]},e.code))})]}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("button",{onClick:()=>d(!l),className:"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors duration-200",title:"Settings",children:a.jsx(eb,{className:"w-5 h-5"})}),l&&(0,a.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-56 bg-white rounded-lg shadow-lg border border-slate-200 py-2 z-50",children:[a.jsx("div",{className:"px-4 py-2 border-b border-slate-100",children:a.jsx("p",{className:"text-sm font-medium text-slate-900",children:"Settings"})}),a.jsx("button",{onClick:()=>{d(!1)},className:"w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors duration-200",children:"Preferences"}),a.jsx("button",{onClick:()=>{d(!1)},className:"w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors duration-200",children:"About"}),a.jsx("div",{className:"border-t border-slate-100 mt-2 pt-2",children:(0,a.jsxs)("div",{className:"px-4 py-2",children:[a.jsx("p",{className:"text-xs text-slate-500",children:"Version 1.0.0"}),(0,a.jsxs)("p",{className:"text-xs text-slate-500",children:["\xa9 2024 ",eh.name]})]})})]})]})]})]}),(o||l)&&a.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>{s(!1),d(!1)}})]})}let ey=l("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),ew=l("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function ej({onStartChat:e,language:t}){em[t];let n=[{icon:m,title:"en"===t?"Company Knowledge":"fr"===t?"Connaissances de l'entreprise":"معرفة الشركة",description:"en"===t?"Ask about Flexible Soft services, policies, and internal documentation":"fr"===t?"Posez des questions sur les services, politiques et documentation interne de Flexible Soft":"اسأل عن خدمات وسياسات ووثائق Flexible Soft الداخلية"},{icon:ey,title:"en"===t?"Technical Expertise":"fr"===t?"Expertise technique":"الخبرة التقنية",description:"en"===t?"Get expert guidance on programming, AI, and software development":"fr"===t?"Obtenez des conseils d'expert sur la programmation, l'IA et le d\xe9veloppement logiciel":"احصل على إرشادات خبير في البرمجة والذكاء الاصطناعي وتطوير البرمجيات"},{icon:x,title:"en"===t?"Document Search":"fr"===t?"Recherche de documents":"البحث في المستندات",description:"en"===t?"Search through uploaded company documents and knowledge base":"fr"===t?"Recherchez dans les documents d'entreprise t\xe9l\xe9charg\xe9s et la base de connaissances":"ابحث في مستندات الشركة المرفوعة وقاعدة المعرفة"},{icon:ew,title:"en"===t?"Instant Answers":"fr"===t?"R\xe9ponses instantan\xe9es":"إجابات فورية",description:"en"===t?"Get quick, accurate responses powered by advanced AI technology":"fr"===t?"Obtenez des r\xe9ponses rapides et pr\xe9cises aliment\xe9es par une technologie IA avanc\xe9e":"احصل على إجابات سريعة ودقيقة مدعومة بتقنية الذكاء الاصطناعي المتقدمة"}];return a.jsx("div",{className:"flex-1 overflow-y-auto custom-scrollbar",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-6 py-12",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-2xl mb-6",children:a.jsx("span",{className:"text-2xl font-bold text-primary-600",children:"FS"})}),a.jsx("h1",{className:"text-4xl font-bold text-slate-900 mb-4 gradient-text",children:"en"===t?`Welcome to ${ec.companyName}`:"fr"===t?`Bienvenue chez ${ec.companyName}`:`مرحباً بك في ${ec.companyName}`}),a.jsx("p",{className:"text-xl text-slate-600 mb-8 max-w-2xl mx-auto",children:"en"===t?"Your AI-powered knowledge assistant for all things related to software development, company policies, and technical guidance.":"fr"===t?"Votre assistant de connaissances aliment\xe9 par l'IA pour tout ce qui concerne le d\xe9veloppement logiciel, les politiques d'entreprise et les conseils techniques.":"مساعدك المعرفي المدعوم بالذكاء الاصطناعي لكل ما يتعلق بتطوير البرمجيات وسياسات الشركة والإرشادات التقنية."}),a.jsx("button",{onClick:e,className:"btn-primary text-lg px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",children:"en"===t?"Start Chatting":"fr"===t?"Commencer \xe0 discuter":"ابدأ المحادثة"})]}),a.jsx("div",{className:"grid md:grid-cols-2 gap-6 mb-12",children:n.map((e,t)=>a.jsx("div",{className:"bg-white rounded-xl p-6 shadow-soft hover:shadow-medium transition-shadow duration-200 border border-slate-100",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[a.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center",children:a.jsx(e.icon,{className:"w-6 h-6 text-primary-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-slate-900 mb-2",children:e.title}),a.jsx("p",{className:"text-slate-600 leading-relaxed",children:e.description})]})]})},t))}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-soft border border-slate-100",children:[a.jsx("h3",{className:"text-lg font-semibold text-slate-900 mb-4",children:"en"===t?"Try asking:":"fr"===t?"Essayez de demander :":"جرب أن تسأل:"}),a.jsx("div",{className:"grid sm:grid-cols-2 gap-3",children:("en"===t?["What services does Flexible Soft offer?","How do I submit a project request?","What technologies does the team use?","Tell me about React best practices"]:"fr"===t?["Quels services propose Flexible Soft ?","Comment soumettre une demande de projet ?","Quelles technologies utilise l'\xe9quipe ?","Parlez-moi des meilleures pratiques React"]:["ما هي الخدمات التي تقدمها Flexible Soft؟","كيف أقدم طلب مشروع؟","ما هي التقنيات التي يستخدمها الفريق؟","أخبرني عن أفضل ممارسات React"]).map((t,n)=>(0,a.jsxs)("button",{onClick:()=>{e()},className:"text-left p-3 bg-slate-50 hover:bg-slate-100 rounded-lg transition-colors duration-200 text-slate-700 hover:text-slate-900",children:['"',t,'"']},n))})]}),(0,a.jsxs)("div",{className:"mt-12 text-center",children:[(0,a.jsxs)("div",{className:"inline-flex items-center gap-2 text-sm text-slate-500 bg-slate-50 px-4 py-2 rounded-full",children:[a.jsx("span",{children:"Powered by"}),a.jsx("span",{className:"font-semibold text-slate-700",children:eh.name}),a.jsx("span",{children:"•"}),a.jsx("span",{children:eh.description})]}),a.jsx("div",{className:"mt-4 flex flex-wrap justify-center gap-2",children:eh.technologies.map((e,t)=>a.jsx("span",{className:"inline-block px-3 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full",children:e},t))})]})]})})}var eN=n(381);function ek(){let[e,t]=(0,r.useState)(null),[n,o]=(0,r.useState)([]),[s,l]=(0,r.useState)(!1),[d,c]=(0,r.useState)("en"),[u,m]=(0,r.useState)(!0),h=()=>{let e={id:eo(),messages:[],created_at:new Date,updated_at:new Date,title:"New Chat"};o(t=>[e,...t]),t(e),l(!1)};return u?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"spinner mx-auto mb-4"}),(0,a.jsxs)("p",{className:"text-slate-600",children:["Loading ",ec.appName,"..."]})]})}):(0,a.jsxs)("div",{className:"flex h-screen bg-gradient-to-br from-slate-50 to-blue-50",children:[a.jsx(ef,{isOpen:s,onClose:()=>l(!1),sessions:n,currentSession:e,onNewSession:h,onSelectSession:e=>{t(e),l(!1)},onDeleteSession:n=>{o(e=>e.filter(e=>e.id!==n)),e?.id===n&&t(null)},onClearAll:()=>{o([]),t(null),localStorage.removeItem(eu.chatSessions),localStorage.removeItem(eu.currentSession),eN.Am.success("All chat sessions cleared")},language:d}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[a.jsx(ev,{onToggleSidebar:()=>l(!s),currentSession:e,language:d,onLanguageChange:c}),a.jsx("div",{className:"flex-1 overflow-hidden",children:e?a.jsx(i.ChatInterface,{session:e,onUpdateSession:n=>{o(e=>e.map(e=>e.id===n.id?n:e)),e?.id===n.id&&t(n)},language:d}):a.jsx(ej,{onStartChat:h,language:d})})]}),s&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>l(!1)})]})}},2695:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[38;2;255;30;30m\xd7\x1b[0m Expected ';', '}' or <eof>\n     ╭─[\x1b[38;2;92;157;255;1;4mC:\\Users\\<USER>\\Documents\\augment-projects\\Chatyy\\frontend\\src\\components\\chat\\ChatInterface.tsx\x1b[0m:128:1]\n \x1b[2m128\x1b[0m │               console.error('Streaming error:', error);\n \x1b[2m129\x1b[0m │               setError(error || 'Failed to send message');\n \x1b[2m130\x1b[0m │     \n \x1b[2m131\x1b[0m │ \x1b[38;2;246;87;248m╭\x1b[0m\x1b[38;2;246;87;248m─\x1b[0m\x1b[38;2;246;87;248m▶\x1b[0m             content: DEFAULT_MESSAGES[language].error,\n \x1b[2m132\x1b[0m │ \x1b[38;2;246;87;248m├\x1b[0m\x1b[38;2;246;87;248m─\x1b[0m\x1b[38;2;246;87;248m▶\x1b[0m             timestamp: new Date(),\n     \xb7 \x1b[38;2;246;87;248m╰\x1b[0m\x1b[38;2;246;87;248m───\x1b[0m\x1b[38;2;30;201;212m                     ─\x1b[0m\n     \xb7 \x1b[38;2;246;87;248m╰\x1b[0m\x1b[38;2;246;87;248m───\x1b[0m\x1b[38;2;246;87;248m─\x1b[0m \x1b[38;2;246;87;248mThis is the expression part of an expression statement\x1b[0m\n \x1b[2m133\x1b[0m │               };\n \x1b[2m134\x1b[0m │     \n \x1b[2m135\x1b[0m │               const errorSession = {\n     ╰────\n\n\nCaused by:\n    Syntax Error")},3525:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d,metadata:()=>l});var a=n(9510),r=n(5384),i=n.n(r),o=n(9125);n(5023);let s={apiUrl:"http://localhost:8000",appName:"Flexible Soft Assistant",companyName:"Flexible Soft",maxMessageLength:parseInt("2000"),maxFileSize:parseInt("10485760"),allowedFileTypes:"pdf,docx,txt,md".split(","),supportedLanguages:"en,fr,ar".split(","),defaultLanguage:"en",enableFileUpload:!0,enableFeedback:!0,enableMultilingual:!0},l={title:s.appName,description:`AI-powered knowledge assistant for ${s.companyName}`,keywords:["AI","chatbot","assistant","Flexible Soft","Sofflex"],authors:[{name:s.companyName}],viewport:"width=device-width, initial-scale=1",robots:"noindex, nofollow"};function d({children:e}){return a.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:(0,a.jsxs)("body",{className:i().className,children:[a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50",children:e}),a.jsx(o.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#10b981",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})}},5480:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});let a=(0,n(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\Chatyy\frontend\src\app\page.tsx#default`)},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),a=t.X(0,[731],()=>n(152));module.exports=a})();
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CPC4%5CDocuments%5Caugment-projects%5CChatyy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC4%5CDocuments%5Caugment-projects%5CChatyy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CPC4%5CDocuments%5Caugment-projects%5CChatyy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC4%5CDocuments%5Caugment-projects%5CChatyy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CPC4%5CDocuments%5Caugment-projects%5CChatyy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC4%5CDocuments%5Caugment-projects%5CChatyy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDNCU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNDaGF0eXklNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNQQzQlNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDQ2hhdHl5JTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNyZWFjdC1ob3QtdG9hc3QlNUMlNUNkaXN0JTVDJTVDaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNQQzQlNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDQ2hhdHl5JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc01BQStLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmxleGlibGUtc29mdC1jaGF0Ym90LWZyb250ZW5kLz9hNzRjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFBDNFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxDaGF0eXlcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDNCU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNDaGF0eXklNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBc0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mbGV4aWJsZS1zb2Z0LWNoYXRib3QtZnJvbnRlbmQvPzE5M2IiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQQzRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcQ2hhdHl5XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC4%5C%5CDocuments%5C%5Caugment-projects%5C%5CChatyy%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_chat_ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/chat/ChatInterface */ \"(ssr)/./src/components/chat/ChatInterface.tsx\");\n/* harmony import */ var _components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_chat_WelcomeScreen__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/WelcomeScreen */ \"(ssr)/./src/components/chat/WelcomeScreen.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction HomePage() {\n    const [currentSession, setCurrentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"en\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Load data from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            // Load sessions\n            const savedSessions = localStorage.getItem(_lib_config__WEBPACK_IMPORTED_MODULE_7__.STORAGE_KEYS.chatSessions);\n            if (savedSessions) {\n                const parsedSessions = JSON.parse(savedSessions).map((session)=>({\n                        ...session,\n                        created_at: new Date(session.created_at),\n                        updated_at: new Date(session.updated_at),\n                        messages: session.messages.map((msg)=>({\n                                ...msg,\n                                timestamp: new Date(msg.timestamp)\n                            }))\n                    }));\n                setSessions(parsedSessions);\n            }\n            // Load current session\n            const savedCurrentSession = localStorage.getItem(_lib_config__WEBPACK_IMPORTED_MODULE_7__.STORAGE_KEYS.currentSession);\n            if (savedCurrentSession) {\n                const sessionId = JSON.parse(savedCurrentSession);\n                const session = sessions.find((s)=>s.id === sessionId);\n                if (session) {\n                    setCurrentSession(session);\n                }\n            }\n            // Load user preferences\n            const savedPreferences = localStorage.getItem(_lib_config__WEBPACK_IMPORTED_MODULE_7__.STORAGE_KEYS.userPreferences);\n            if (savedPreferences) {\n                const preferences = JSON.parse(savedPreferences);\n                setLanguage(preferences.language || \"en\");\n            }\n        } catch (error) {\n            console.error(\"Error loading data from localStorage:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load saved data\");\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    // Save sessions to localStorage whenever they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sessions.length > 0) {\n            try {\n                localStorage.setItem(_lib_config__WEBPACK_IMPORTED_MODULE_7__.STORAGE_KEYS.chatSessions, JSON.stringify(sessions));\n            } catch (error) {\n                console.error(\"Error saving sessions:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to save chat sessions\");\n            }\n        }\n    }, [\n        sessions\n    ]);\n    // Save current session to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentSession) {\n            try {\n                localStorage.setItem(_lib_config__WEBPACK_IMPORTED_MODULE_7__.STORAGE_KEYS.currentSession, JSON.stringify(currentSession.id));\n            } catch (error) {\n                console.error(\"Error saving current session:\", error);\n            }\n        }\n    }, [\n        currentSession\n    ]);\n    // Save user preferences\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            const preferences = {\n                language,\n                theme: \"light\",\n                notifications: true,\n                autoScroll: true,\n                showTimestamps: true\n            };\n            localStorage.setItem(_lib_config__WEBPACK_IMPORTED_MODULE_7__.STORAGE_KEYS.userPreferences, JSON.stringify(preferences));\n        } catch (error) {\n            console.error(\"Error saving preferences:\", error);\n        }\n    }, [\n        language\n    ]);\n    const createNewSession = ()=>{\n        const newSession = {\n            id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.generateId)(),\n            messages: [],\n            created_at: new Date(),\n            updated_at: new Date(),\n            title: \"New Chat\"\n        };\n        setSessions((prev)=>[\n                newSession,\n                ...prev\n            ]);\n        setCurrentSession(newSession);\n        setSidebarOpen(false);\n    };\n    const selectSession = (session)=>{\n        setCurrentSession(session);\n        setSidebarOpen(false);\n    };\n    const updateSession = (updatedSession)=>{\n        setSessions((prev)=>prev.map((session)=>session.id === updatedSession.id ? updatedSession : session));\n        if (currentSession?.id === updatedSession.id) {\n            setCurrentSession(updatedSession);\n        }\n    };\n    const deleteSession = (sessionId)=>{\n        setSessions((prev)=>prev.filter((session)=>session.id !== sessionId));\n        if (currentSession?.id === sessionId) {\n            setCurrentSession(null);\n        }\n    };\n    const clearAllSessions = ()=>{\n        setSessions([]);\n        setCurrentSession(null);\n        localStorage.removeItem(_lib_config__WEBPACK_IMPORTED_MODULE_7__.STORAGE_KEYS.chatSessions);\n        localStorage.removeItem(_lib_config__WEBPACK_IMPORTED_MODULE_7__.STORAGE_KEYS.currentSession);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"All chat sessions cleared\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-600\",\n                        children: [\n                            \"Loading \",\n                            _lib_config__WEBPACK_IMPORTED_MODULE_7__.config.appName,\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                isOpen: sidebarOpen,\n                onClose: ()=>setSidebarOpen(false),\n                sessions: sessions,\n                currentSession: currentSession,\n                onNewSession: createNewSession,\n                onSelectSession: selectSession,\n                onDeleteSession: deleteSession,\n                onClearAll: clearAllSessions,\n                language: language\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_4__.Header, {\n                        onToggleSidebar: ()=>setSidebarOpen(!sidebarOpen),\n                        currentSession: currentSession,\n                        language: language,\n                        onLanguageChange: setLanguage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: currentSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatInterface__WEBPACK_IMPORTED_MODULE_2__.ChatInterface, {\n                            session: currentSession,\n                            onUpdateSession: updateSession,\n                            language: language\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_WelcomeScreen__WEBPACK_IMPORTED_MODULE_5__.WelcomeScreen, {\n                            onStartChat: createNewSession,\n                            language: language\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ChatInterface.tsx":
/*!***********************************************!*\
  !*** ./src/components/chat/ChatInterface.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MessageList__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageList */ \"(ssr)/./src/components/chat/MessageList.tsx\");\n/* harmony import */ var _MessageInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageInput */ \"(ssr)/./src/components/chat/MessageInput.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TypingIndicator */ \"(ssr)/./src/components/chat/TypingIndicator.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \n\n\n\n\n\n\n\n\nfunction ChatInterface({ session, onUpdateSession, language }) {\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (messagesEndRef.current) {\n            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.scrollToBottom)(messagesEndRef.current.parentElement);\n        }\n    }, [\n        session.messages,\n        isTyping\n    ]);\n    // Add welcome message if session is empty\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (session.messages.length === 0) {\n            const welcomeMessage = {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.generateId)(),\n                role: \"assistant\",\n                content: _lib_config__WEBPACK_IMPORTED_MODULE_7__.DEFAULT_MESSAGES[language].welcome,\n                timestamp: new Date()\n            };\n            const updatedSession = {\n                ...session,\n                messages: [\n                    welcomeMessage\n                ],\n                updated_at: new Date()\n            };\n            onUpdateSession(updatedSession);\n        }\n    }, [\n        session,\n        language,\n        onUpdateSession\n    ]);\n    const handleSendMessage = async (content)=>{\n        if (!content.trim() || isTyping || isStreaming) return;\n        setError(null);\n        setStreamingMessage(\"\");\n        // Create user message\n        const userMessage = {\n            id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.generateId)(),\n            role: \"user\",\n            content: content.trim(),\n            timestamp: new Date()\n        };\n        // Update session with user message\n        const updatedMessages = [\n            ...session.messages,\n            userMessage\n        ];\n        let updatedSession = {\n            ...session,\n            messages: updatedMessages,\n            updated_at: new Date()\n        };\n        // Update title if this is the first user message\n        if (session.messages.filter((m)=>m.role === \"user\").length === 0) {\n            updatedSession.title = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.generateSessionTitle)(updatedMessages);\n        }\n        onUpdateSession(updatedSession);\n        setIsTyping(true);\n        setIsStreaming(true);\n        try {\n            // Use streaming API for better user experience\n            await (0,_lib_api__WEBPACK_IMPORTED_MODULE_5__.sendMessageStream)({\n                message: content,\n                session_id: session.id,\n                language\n            }, // onChunk - called for each piece of the response\n            (chunk)=>{\n                setStreamingMessage((prev)=>prev + chunk);\n            }, // onComplete - called when response is complete\n            (fullResponse)=>{\n                setIsStreaming(false);\n                setStreamingMessage(\"\");\n                // Create assistant message\n                const assistantMessage = {\n                    id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.generateId)(),\n                    role: \"assistant\",\n                    content: fullResponse,\n                    timestamp: new Date(),\n                    metadata: {\n                        sources: [\n                            \"FS - Flexible Soft AI Assistant (Enhanced LLaMA-Powered)\"\n                        ],\n                        language: language\n                    }\n                };\n                // Update session with assistant message\n                const finalMessages = [\n                    ...updatedMessages,\n                    assistantMessage\n                ];\n                const finalSession = {\n                    ...updatedSession,\n                    messages: finalMessages,\n                    updated_at: new Date()\n                };\n                onUpdateSession(finalSession);\n            }, // onError - called if streaming fails\n            (error)=>{\n                setIsStreaming(false);\n                setStreamingMessage(\"\");\n                console.error(\"Streaming error:\", error);\n                setError(error || \"Failed to send message\");\n                // Create error message\n                const errorMessage = {\n                    id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.generateId)(),\n                    role: \"assistant\",\n                    content: _lib_config__WEBPACK_IMPORTED_MODULE_7__.DEFAULT_MESSAGES[language].error,\n                    timestamp: new Date()\n                };\n                const errorSession = {\n                    ...updatedSession,\n                    messages: [\n                        ...updatedMessages,\n                        errorMessage\n                    ],\n                    updated_at: new Date()\n                };\n                onUpdateSession(errorSession);\n            });\n        } catch (error) {\n            setIsStreaming(false);\n            setStreamingMessage(\"\");\n            console.error(\"Error sending message:\", error);\n            setError(error.message || \"Failed to send message\");\n            // Create error message\n            const errorMessage = {\n                id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.generateId)(),\n                role: \"assistant\",\n                content: _lib_config__WEBPACK_IMPORTED_MODULE_7__.DEFAULT_MESSAGES[language].error,\n                timestamp: new Date()\n            };\n            const errorSession = {\n                ...updatedSession,\n                messages: [\n                    ...updatedMessages,\n                    errorMessage\n                ],\n                updated_at: new Date()\n            };\n            onUpdateSession(errorSession);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.error(error.message || \"Failed to send message\");\n        } finally{\n            setIsTyping(false);\n        }\n    };\n    const handleRetryMessage = (messageId)=>{\n        const messageIndex = session.messages.findIndex((m)=>m.id === messageId);\n        if (messageIndex === -1) return;\n        const message = session.messages[messageIndex];\n        if (message.role !== \"user\") return;\n        // Remove the user message and any subsequent messages\n        const updatedMessages = session.messages.slice(0, messageIndex);\n        const updatedSession = {\n            ...session,\n            messages: updatedMessages,\n            updated_at: new Date()\n        };\n        onUpdateSession(updatedSession);\n        // Resend the message\n        handleSendMessage(message.content);\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Message copied to clipboard\");\n        }).catch(()=>{\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to copy message\");\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: messagesContainerRef,\n                className: \"flex-1 overflow-y-auto custom-scrollbar px-4 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageList__WEBPACK_IMPORTED_MODULE_2__.MessageList, {\n                            messages: session.messages,\n                            language: language,\n                            onRetry: handleRetryMessage,\n                            onCopy: handleCopyMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        isStreaming && streamingMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: \"FS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-sm border p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose prose-sm max-w-none\",\n                                                children: [\n                                                    streamingMessage,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block w-2 h-4 bg-blue-500 animate-pulse ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this),\n                        isTyping && !isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__.TypingIndicator, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-700 text-sm\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setError(null),\n                                    className: \"mt-2 text-red-600 hover:text-red-800 text-sm underline\",\n                                    children: \"Dismiss\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-slate-200 bg-white/80 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInput__WEBPACK_IMPORTED_MODULE_3__.MessageInput, {\n                        onSendMessage: handleSendMessage,\n                        disabled: isTyping || isStreaming,\n                        language: language,\n                        placeholder: isStreaming ? \"FS is responding...\" : _lib_config__WEBPACK_IMPORTED_MODULE_7__.DEFAULT_MESSAGES[language].placeholder\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\ChatInterface.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ChatInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/MessageBubble.tsx":
/*!***********************************************!*\
  !*** ./src/components/chat/MessageBubble.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageBubble: () => (/* binding */ MessageBubble)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Copy_ExternalLink_RotateCcw_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,ExternalLink,RotateCcw,ThumbsDown,ThumbsUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_ExternalLink_RotateCcw_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,ExternalLink,RotateCcw,ThumbsDown,ThumbsUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_ExternalLink_RotateCcw_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,ExternalLink,RotateCcw,ThumbsDown,ThumbsUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_ExternalLink_RotateCcw_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,ExternalLink,RotateCcw,ThumbsDown,ThumbsUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_ExternalLink_RotateCcw_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,ExternalLink,RotateCcw,ThumbsDown,ThumbsUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _MessageContent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageContent */ \"(ssr)/./src/components/chat/MessageContent.tsx\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ MessageBubble auto */ \n\n\n\n\n\nfunction MessageBubble({ message, language, onRetry, onCopy, showActions = false }) {\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    const handleCopy = ()=>{\n        onCopy(message.content);\n    };\n    const handleRetry = ()=>{\n        onRetry(message.id);\n    };\n    const handleFeedback = (type)=>{\n        setFeedback(type);\n    // TODO: Send feedback to API\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group relative max-w-[80%] sm:max-w-[70%]\", isUser && \"ml-auto\", isAssistant && \"mr-auto\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"message-bubble rounded-2xl px-4 py-3 shadow-sm transition-all duration-200\", isUser && \"bg-primary-600 text-white\", isAssistant && \"bg-white text-slate-900 border border-slate-200\", \"hover:shadow-md\"),\n                children: [\n                    isAssistant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2 text-xs text-slate-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-5 h-5 bg-primary-100 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-primary-600 font-bold text-xs\",\n                                    children: \"FS\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    _lib_config__WEBPACK_IMPORTED_MODULE_4__.config.companyName,\n                                    \" Assistant\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageContent__WEBPACK_IMPORTED_MODULE_3__.MessageContent, {\n                        content: message.content,\n                        language: language\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    isAssistant && message.metadata?.sources && message.metadata.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-slate-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-slate-500 mb-2\",\n                                children: \"Sources:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-1\",\n                                children: message.metadata.sources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center gap-1 text-xs bg-slate-100 text-slate-600 px-2 py-1 rounded-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_ExternalLink_RotateCcw_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this),\n                                            source\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    isAssistant && message.metadata?.confidence && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-xs text-slate-500\",\n                        children: [\n                            \"Confidence: \",\n                            Math.round(message.metadata.confidence * 100),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center gap-1 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\", isUser ? \"justify-end\" : \"justify-start\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"p-1.5 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-md transition-colors duration-200\",\n                        title: \"Copy message\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_ExternalLink_RotateCcw_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this),\n                    isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleRetry,\n                        className: \"p-1.5 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-md transition-colors duration-200\",\n                        title: \"Retry message\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_ExternalLink_RotateCcw_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 13\n                    }, this),\n                    isAssistant && _lib_config__WEBPACK_IMPORTED_MODULE_4__.config.enableFeedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleFeedback(\"up\"),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1.5 rounded-md transition-colors duration-200\", feedback === \"up\" ? \"text-green-600 bg-green-100\" : \"text-slate-400 hover:text-green-600 hover:bg-green-50\"),\n                                title: \"Good response\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_ExternalLink_RotateCcw_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleFeedback(\"down\"),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1.5 rounded-md transition-colors duration-200\", feedback === \"down\" ? \"text-red-600 bg-red-100\" : \"text-slate-400 hover:text-red-600 hover:bg-red-50\"),\n                                title: \"Poor response\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_ExternalLink_RotateCcw_ThumbsDown_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this),\n            showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-800 text-white text-xs rounded whitespace-nowrap z-10\",\n                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatRelativeTime)(message.timestamp)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/MessageBubble.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/MessageContent.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/MessageContent.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageContent: () => (/* binding */ MessageContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-syntax-highlighter */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/prism.js\");\n/* harmony import */ var react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-syntax-highlighter/dist/esm/styles/prism */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/one-dark.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MessageContent auto */ \n\n\n\n\n\n\n\nfunction MessageContent({ content, language }) {\n    const [copiedCode, setCopiedCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleCopyCode = async (code)=>{\n        try {\n            await navigator.clipboard.writeText(code);\n            setCopiedCode(code);\n            setTimeout(()=>setCopiedCode(null), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy code:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"prose prose-sm max-w-none\", language === \"ar\" && \"prose-rtl\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n            ],\n            components: {\n                // Custom code block component\n                code ({ node, className, children, ...props }) {\n                    const inline = !className;\n                    const match = /language-(\\w+)/.exec(className || \"\");\n                    const codeContent = String(children).replace(/\\n$/, \"\");\n                    if (!inline && match) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between bg-slate-800 text-slate-200 px-4 py-2 text-sm rounded-t-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: match[1]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleCopyCode(codeContent),\n                                            className: \"flex items-center gap-1 px-2 py-1 text-xs bg-slate-700 hover:bg-slate-600 rounded transition-colors duration-200\",\n                                            children: copiedCode === codeContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                                                        lineNumber: 55,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    \"Copied\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    \"Copy\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                    language: match[1],\n                                    PreTag: \"div\",\n                                    className: \"!mt-0 !rounded-t-none\",\n                                    ...props,\n                                    children: codeContent\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-slate-100 text-slate-900 px-1 py-0.5 rounded text-sm\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Custom link component\n                a ({ href, children, ...props }) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"text-primary-600 hover:text-primary-700 underline\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Custom list components\n                ul ({ children, ...props }) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside space-y-1\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                ol ({ children, ...props }) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside space-y-1\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Custom blockquote component\n                blockquote ({ children, ...props }) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-primary-200 pl-4 py-2 bg-primary-50 rounded-r-lg italic\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Custom table components\n                table ({ children, ...props }) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border border-slate-200 rounded-lg\",\n                            ...props,\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                th ({ children, ...props }) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"px-4 py-2 bg-slate-50 border-b border-slate-200 text-left font-medium text-slate-900\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                td ({ children, ...props }) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-4 py-2 border-b border-slate-200\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Custom heading components\n                h1 ({ children, ...props }) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-bold text-slate-900 mb-3\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                h2 ({ children, ...props }) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-slate-900 mb-2\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                h3 ({ children, ...props }) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-medium text-slate-900 mb-2\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Custom paragraph component\n                p ({ children, ...props }) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-3 leading-relaxed\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 15\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageContent.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/MessageContent.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/MessageInput.tsx":
/*!**********************************************!*\
  !*** ./src/components/chat/MessageInput.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageInput: () => (/* binding */ MessageInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Paperclip,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Paperclip,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Paperclip,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Paperclip,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ MessageInput auto */ \n\n\n\n\nfunction MessageInput({ onSendMessage, disabled = false, language, placeholder = \"Type your message...\" }) {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const direction = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getLanguageDirection)(language);\n    const handleSubmit = ()=>{\n        if (!message.trim() || disabled) return;\n        onSendMessage(message.trim());\n        setMessage(\"\");\n        // Reset textarea height\n        if (textareaRef.current) {\n            textareaRef.current.style.height = \"auto\";\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit();\n        }\n    };\n    const handleInputChange = (e)=>{\n        const value = e.target.value;\n        // Enforce character limit\n        if (value.length <= _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.maxMessageLength) {\n            setMessage(value);\n        }\n        // Auto-resize textarea\n        const textarea = e.target;\n        textarea.style.height = \"auto\";\n        textarea.style.height = Math.min(textarea.scrollHeight, 120) + \"px\";\n    };\n    const handleFileUpload = ()=>{\n        // TODO: Implement file upload\n        console.log(\"File upload clicked\");\n    };\n    const handleVoiceRecording = ()=>{\n        if (isRecording) {\n            // Stop recording\n            setIsRecording(false);\n        // TODO: Implement voice recording stop\n        } else {\n            // Start recording\n            setIsRecording(true);\n        // TODO: Implement voice recording start\n        }\n    };\n    const isMessageValid = message.trim().length > 0 && message.length <= _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.maxMessageLength;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${direction === \"rtl\" ? \"rtl\" : \"ltr\"}`,\n        dir: direction,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end gap-3 p-4 bg-white rounded-2xl shadow-lg border border-slate-200\",\n                        children: [\n                            _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.enableFileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleFileUpload,\n                                disabled: disabled,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-shrink-0 p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors duration-200\", disabled && \"opacity-50 cursor-not-allowed\"),\n                                title: \"Attach file\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: textareaRef,\n                                        value: message,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyDown,\n                                        placeholder: placeholder,\n                                        disabled: disabled,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full resize-none border-0 bg-transparent text-slate-900 placeholder-slate-500 focus:outline-none focus:ring-0\", \"min-h-[24px] max-h-[120px] py-2\", direction === \"rtl\" && \"text-right\"),\n                                        rows: 1\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    message.length > _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.maxMessageLength * 0.8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute -bottom-6 text-xs\", direction === \"rtl\" ? \"left-0\" : \"right-0\", message.length > _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.maxMessageLength ? \"text-red-500\" : \"text-slate-400\"),\n                                        children: [\n                                            message.length,\n                                            \"/\",\n                                            _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.maxMessageLength\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleVoiceRecording,\n                                disabled: disabled,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-shrink-0 p-2 rounded-lg transition-colors duration-200\", isRecording ? \"text-red-500 bg-red-50 hover:bg-red-100\" : \"text-slate-400 hover:text-slate-600 hover:bg-slate-100\", disabled && \"opacity-50 cursor-not-allowed\"),\n                                title: isRecording ? \"Stop recording\" : \"Start voice recording\",\n                                children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSubmit,\n                                disabled: disabled || !isMessageValid,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-shrink-0 p-2 rounded-lg transition-all duration-200\", isMessageValid && !disabled ? \"bg-primary-600 text-white hover:bg-primary-700 shadow-md hover:shadow-lg\" : \"bg-slate-100 text-slate-400 cursor-not-allowed\"),\n                                title: \"Send message\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Paperclip_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-3 py-1 rounded-full text-sm flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            \"Recording...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-3 text-xs text-slate-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Press Enter to send, Shift+Enter for new line\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-primary-500 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"AI is thinking...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageInput.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jaGF0L01lc3NhZ2VJbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRXdEO0FBQ0k7QUFFTDtBQUNqQjtBQVMvQixTQUFTUyxhQUFhLEVBQzNCQyxhQUFhLEVBQ2JDLFdBQVcsS0FBSyxFQUNoQkMsUUFBUSxFQUNSQyxjQUFjLHNCQUFzQixFQUNsQjtJQUNsQixNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR2YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDZ0IsYUFBYUMsZUFBZSxHQUFHakIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTWtCLGNBQWNqQiw2Q0FBTUEsQ0FBc0I7SUFDaEQsTUFBTWtCLFlBQVlaLGdFQUFvQkEsQ0FBQ0s7SUFFdkMsTUFBTVEsZUFBZTtRQUNuQixJQUFJLENBQUNOLFFBQVFPLElBQUksTUFBTVYsVUFBVTtRQUVqQ0QsY0FBY0ksUUFBUU8sSUFBSTtRQUMxQk4sV0FBVztRQUVYLHdCQUF3QjtRQUN4QixJQUFJRyxZQUFZSSxPQUFPLEVBQUU7WUFDdkJKLFlBQVlJLE9BQU8sQ0FBQ0MsS0FBSyxDQUFDQyxNQUFNLEdBQUc7UUFDckM7SUFDRjtJQUVBLE1BQU1DLGdCQUFnQixDQUFDQztRQUNyQixJQUFJQSxFQUFFQyxHQUFHLEtBQUssV0FBVyxDQUFDRCxFQUFFRSxRQUFRLEVBQUU7WUFDcENGLEVBQUVHLGNBQWM7WUFDaEJUO1FBQ0Y7SUFDRjtJQUVBLE1BQU1VLG9CQUFvQixDQUFDSjtRQUN6QixNQUFNSyxRQUFRTCxFQUFFTSxNQUFNLENBQUNELEtBQUs7UUFFNUIsMEJBQTBCO1FBQzFCLElBQUlBLE1BQU1FLE1BQU0sSUFBSXpCLCtDQUFNQSxDQUFDMEIsZ0JBQWdCLEVBQUU7WUFDM0NuQixXQUFXZ0I7UUFDYjtRQUVBLHVCQUF1QjtRQUN2QixNQUFNSSxXQUFXVCxFQUFFTSxNQUFNO1FBQ3pCRyxTQUFTWixLQUFLLENBQUNDLE1BQU0sR0FBRztRQUN4QlcsU0FBU1osS0FBSyxDQUFDQyxNQUFNLEdBQUdZLEtBQUtDLEdBQUcsQ0FBQ0YsU0FBU0csWUFBWSxFQUFFLE9BQU87SUFDakU7SUFFQSxNQUFNQyxtQkFBbUI7UUFDdkIsOEJBQThCO1FBQzlCQyxRQUFRQyxHQUFHLENBQUM7SUFDZDtJQUVBLE1BQU1DLHVCQUF1QjtRQUMzQixJQUFJMUIsYUFBYTtZQUNmLGlCQUFpQjtZQUNqQkMsZUFBZTtRQUNmLHVDQUF1QztRQUN6QyxPQUFPO1lBQ0wsa0JBQWtCO1lBQ2xCQSxlQUFlO1FBQ2Ysd0NBQXdDO1FBQzFDO0lBQ0Y7SUFFQSxNQUFNMEIsaUJBQWlCN0IsUUFBUU8sSUFBSSxHQUFHWSxNQUFNLEdBQUcsS0FBS25CLFFBQVFtQixNQUFNLElBQUl6QiwrQ0FBTUEsQ0FBQzBCLGdCQUFnQjtJQUU3RixxQkFDRSw4REFBQ1U7UUFBSUMsV0FBVyxDQUFDLEVBQUUxQixjQUFjLFFBQVEsUUFBUSxNQUFNLENBQUM7UUFBRTJCLEtBQUszQjs7MEJBQzdELDhEQUFDeUI7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7NEJBRVpyQywrQ0FBTUEsQ0FBQ3VDLGdCQUFnQixrQkFDdEIsOERBQUNDO2dDQUNDQyxTQUFTVjtnQ0FDVDVCLFVBQVVBO2dDQUNWa0MsV0FBV3ZDLDhDQUFFQSxDQUNYLHNIQUNBSyxZQUFZO2dDQUVkdUMsT0FBTTswQ0FFTiw0RUFBQy9DLHFHQUFTQTtvQ0FBQzBDLFdBQVU7Ozs7Ozs7Ozs7OzBDQUt6Qiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDVjt3Q0FDQ2dCLEtBQUtqQzt3Q0FDTGEsT0FBT2pCO3dDQUNQc0MsVUFBVXRCO3dDQUNWdUIsV0FBVzVCO3dDQUNYWixhQUFhQTt3Q0FDYkYsVUFBVUE7d0NBQ1ZrQyxXQUFXdkMsOENBQUVBLENBQ1gsbUhBQ0EsbUNBQ0FhLGNBQWMsU0FBUzt3Q0FFekJtQyxNQUFNOzs7Ozs7b0NBSVB4QyxRQUFRbUIsTUFBTSxHQUFHekIsK0NBQU1BLENBQUMwQixnQkFBZ0IsR0FBRyxxQkFDMUMsOERBQUNVO3dDQUFJQyxXQUFXdkMsOENBQUVBLENBQ2hCLDhCQUNBYSxjQUFjLFFBQVEsV0FBVyxXQUNqQ0wsUUFBUW1CLE1BQU0sR0FBR3pCLCtDQUFNQSxDQUFDMEIsZ0JBQWdCLEdBQ3BDLGlCQUNBOzs0Q0FFSHBCLFFBQVFtQixNQUFNOzRDQUFDOzRDQUFFekIsK0NBQU1BLENBQUMwQixnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7MENBTS9DLDhEQUFDYztnQ0FDQ0MsU0FBU1A7Z0NBQ1QvQixVQUFVQTtnQ0FDVmtDLFdBQVd2Qyw4Q0FBRUEsQ0FDWCwrREFDQVUsY0FDSSw0Q0FDQSwwREFDSkwsWUFBWTtnQ0FFZHVDLE9BQU9sQyxjQUFjLG1CQUFtQjswQ0FFdkNBLDRCQUNDLDhEQUFDWCxxR0FBTUE7b0NBQUN3QyxXQUFVOzs7Ozt5REFFbEIsOERBQUN6QyxxR0FBR0E7b0NBQUN5QyxXQUFVOzs7Ozs7Ozs7OzswQ0FLbkIsOERBQUNHO2dDQUNDQyxTQUFTN0I7Z0NBQ1RULFVBQVVBLFlBQVksQ0FBQ2dDO2dDQUN2QkUsV0FBV3ZDLDhDQUFFQSxDQUNYLDREQUNBcUMsa0JBQWtCLENBQUNoQyxXQUNmLDZFQUNBO2dDQUVOdUMsT0FBTTswQ0FFTiw0RUFBQ2hELHFHQUFJQTtvQ0FBQzJDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQUtuQjdCLDZCQUNDLDhEQUFDNEI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7Ozs7OzRCQUFvRDs7Ozs7Ozs7Ozs7OzswQkFPekUsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNVO3NDQUFLOzs7Ozs7Ozs7OztvQkFHUDVDLDBCQUNDLDhEQUFDaUM7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDVTswQ0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWxCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmxleGlibGUtc29mdC1jaGF0Ym90LWZyb250ZW5kLy4vc3JjL2NvbXBvbmVudHMvY2hhdC9NZXNzYWdlSW5wdXQudHN4P2U5ODMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlUmVmLCBLZXlib2FyZEV2ZW50IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgU2VuZCwgUGFwZXJjbGlwLCBNaWMsIE1pY09mZiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBMYW5ndWFnZSB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgY24sIGdldExhbmd1YWdlRGlyZWN0aW9uIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuaW1wb3J0IHsgY29uZmlnIH0gZnJvbSAnQC9saWIvY29uZmlnJztcblxuaW50ZXJmYWNlIE1lc3NhZ2VJbnB1dFByb3BzIHtcbiAgb25TZW5kTWVzc2FnZTogKG1lc3NhZ2U6IHN0cmluZykgPT4gdm9pZDtcbiAgZGlzYWJsZWQ/OiBib29sZWFuO1xuICBsYW5ndWFnZTogTGFuZ3VhZ2U7XG4gIHBsYWNlaG9sZGVyPzogc3RyaW5nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gTWVzc2FnZUlucHV0KHsgXG4gIG9uU2VuZE1lc3NhZ2UsIFxuICBkaXNhYmxlZCA9IGZhbHNlLCBcbiAgbGFuZ3VhZ2UsIFxuICBwbGFjZWhvbGRlciA9ICdUeXBlIHlvdXIgbWVzc2FnZS4uLicgXG59OiBNZXNzYWdlSW5wdXRQcm9wcykge1xuICBjb25zdCBbbWVzc2FnZSwgc2V0TWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtpc1JlY29yZGluZywgc2V0SXNSZWNvcmRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCB0ZXh0YXJlYVJlZiA9IHVzZVJlZjxIVE1MVGV4dEFyZWFFbGVtZW50PihudWxsKTtcbiAgY29uc3QgZGlyZWN0aW9uID0gZ2V0TGFuZ3VhZ2VEaXJlY3Rpb24obGFuZ3VhZ2UpO1xuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9ICgpID0+IHtcbiAgICBpZiAoIW1lc3NhZ2UudHJpbSgpIHx8IGRpc2FibGVkKSByZXR1cm47XG4gICAgXG4gICAgb25TZW5kTWVzc2FnZShtZXNzYWdlLnRyaW0oKSk7XG4gICAgc2V0TWVzc2FnZSgnJyk7XG4gICAgXG4gICAgLy8gUmVzZXQgdGV4dGFyZWEgaGVpZ2h0XG4gICAgaWYgKHRleHRhcmVhUmVmLmN1cnJlbnQpIHtcbiAgICAgIHRleHRhcmVhUmVmLmN1cnJlbnQuc3R5bGUuaGVpZ2h0ID0gJ2F1dG8nO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGU6IEtleWJvYXJkRXZlbnQ8SFRNTFRleHRBcmVhRWxlbWVudD4pID0+IHtcbiAgICBpZiAoZS5rZXkgPT09ICdFbnRlcicgJiYgIWUuc2hpZnRLZXkpIHtcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgIGhhbmRsZVN1Ym1pdCgpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MVGV4dEFyZWFFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IHZhbHVlID0gZS50YXJnZXQudmFsdWU7XG4gICAgXG4gICAgLy8gRW5mb3JjZSBjaGFyYWN0ZXIgbGltaXRcbiAgICBpZiAodmFsdWUubGVuZ3RoIDw9IGNvbmZpZy5tYXhNZXNzYWdlTGVuZ3RoKSB7XG4gICAgICBzZXRNZXNzYWdlKHZhbHVlKTtcbiAgICB9XG4gICAgXG4gICAgLy8gQXV0by1yZXNpemUgdGV4dGFyZWFcbiAgICBjb25zdCB0ZXh0YXJlYSA9IGUudGFyZ2V0O1xuICAgIHRleHRhcmVhLnN0eWxlLmhlaWdodCA9ICdhdXRvJztcbiAgICB0ZXh0YXJlYS5zdHlsZS5oZWlnaHQgPSBNYXRoLm1pbih0ZXh0YXJlYS5zY3JvbGxIZWlnaHQsIDEyMCkgKyAncHgnO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUZpbGVVcGxvYWQgPSAoKSA9PiB7XG4gICAgLy8gVE9ETzogSW1wbGVtZW50IGZpbGUgdXBsb2FkXG4gICAgY29uc29sZS5sb2coJ0ZpbGUgdXBsb2FkIGNsaWNrZWQnKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVWb2ljZVJlY29yZGluZyA9ICgpID0+IHtcbiAgICBpZiAoaXNSZWNvcmRpbmcpIHtcbiAgICAgIC8vIFN0b3AgcmVjb3JkaW5nXG4gICAgICBzZXRJc1JlY29yZGluZyhmYWxzZSk7XG4gICAgICAvLyBUT0RPOiBJbXBsZW1lbnQgdm9pY2UgcmVjb3JkaW5nIHN0b3BcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gU3RhcnQgcmVjb3JkaW5nXG4gICAgICBzZXRJc1JlY29yZGluZyh0cnVlKTtcbiAgICAgIC8vIFRPRE86IEltcGxlbWVudCB2b2ljZSByZWNvcmRpbmcgc3RhcnRcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaXNNZXNzYWdlVmFsaWQgPSBtZXNzYWdlLnRyaW0oKS5sZW5ndGggPiAwICYmIG1lc3NhZ2UubGVuZ3RoIDw9IGNvbmZpZy5tYXhNZXNzYWdlTGVuZ3RoO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2Ake2RpcmVjdGlvbiA9PT0gJ3J0bCcgPyAncnRsJyA6ICdsdHInfWB9IGRpcj17ZGlyZWN0aW9ufT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgey8qIE1haW4gSW5wdXQgQ29udGFpbmVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtZW5kIGdhcC0zIHAtNCBiZy13aGl0ZSByb3VuZGVkLTJ4bCBzaGFkb3ctbGcgYm9yZGVyIGJvcmRlci1zbGF0ZS0yMDBcIj5cbiAgICAgICAgICB7LyogRmlsZSBVcGxvYWQgQnV0dG9uICovfVxuICAgICAgICAgIHtjb25maWcuZW5hYmxlRmlsZVVwbG9hZCAmJiAoXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUZpbGVVcGxvYWR9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAnZmxleC1zaHJpbmstMCBwLTIgdGV4dC1zbGF0ZS00MDAgaG92ZXI6dGV4dC1zbGF0ZS02MDAgaG92ZXI6Ymctc2xhdGUtMTAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwJyxcbiAgICAgICAgICAgICAgICBkaXNhYmxlZCAmJiAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIHRpdGxlPVwiQXR0YWNoIGZpbGVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UGFwZXJjbGlwIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiBUZXh0IElucHV0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgcmVmPXt0ZXh0YXJlYVJlZn1cbiAgICAgICAgICAgICAgdmFsdWU9e21lc3NhZ2V9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgb25LZXlEb3duPXtoYW5kbGVLZXlEb3dufVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17cGxhY2Vob2xkZXJ9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAndy1mdWxsIHJlc2l6ZS1ub25lIGJvcmRlci0wIGJnLXRyYW5zcGFyZW50IHRleHQtc2xhdGUtOTAwIHBsYWNlaG9sZGVyLXNsYXRlLTUwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0wJyxcbiAgICAgICAgICAgICAgICAnbWluLWgtWzI0cHhdIG1heC1oLVsxMjBweF0gcHktMicsXG4gICAgICAgICAgICAgICAgZGlyZWN0aW9uID09PSAncnRsJyAmJiAndGV4dC1yaWdodCdcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgcm93cz17MX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBDaGFyYWN0ZXIgQ291bnRlciAqL31cbiAgICAgICAgICAgIHttZXNzYWdlLmxlbmd0aCA+IGNvbmZpZy5tYXhNZXNzYWdlTGVuZ3RoICogMC44ICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICdhYnNvbHV0ZSAtYm90dG9tLTYgdGV4dC14cycsXG4gICAgICAgICAgICAgICAgZGlyZWN0aW9uID09PSAncnRsJyA/ICdsZWZ0LTAnIDogJ3JpZ2h0LTAnLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2UubGVuZ3RoID4gY29uZmlnLm1heE1lc3NhZ2VMZW5ndGggXG4gICAgICAgICAgICAgICAgICA/ICd0ZXh0LXJlZC01MDAnIFxuICAgICAgICAgICAgICAgICAgOiAndGV4dC1zbGF0ZS00MDAnXG4gICAgICAgICAgICAgICl9PlxuICAgICAgICAgICAgICAgIHttZXNzYWdlLmxlbmd0aH0ve2NvbmZpZy5tYXhNZXNzYWdlTGVuZ3RofVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogVm9pY2UgUmVjb3JkaW5nIEJ1dHRvbiAqL31cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVWb2ljZVJlY29yZGluZ31cbiAgICAgICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICdmbGV4LXNocmluay0wIHAtMiByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCcsXG4gICAgICAgICAgICAgIGlzUmVjb3JkaW5nIFxuICAgICAgICAgICAgICAgID8gJ3RleHQtcmVkLTUwMCBiZy1yZWQtNTAgaG92ZXI6YmctcmVkLTEwMCcgXG4gICAgICAgICAgICAgICAgOiAndGV4dC1zbGF0ZS00MDAgaG92ZXI6dGV4dC1zbGF0ZS02MDAgaG92ZXI6Ymctc2xhdGUtMTAwJyxcbiAgICAgICAgICAgICAgZGlzYWJsZWQgJiYgJ29wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkJ1xuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHRpdGxlPXtpc1JlY29yZGluZyA/ICdTdG9wIHJlY29yZGluZycgOiAnU3RhcnQgdm9pY2UgcmVjb3JkaW5nJ31cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7aXNSZWNvcmRpbmcgPyAoXG4gICAgICAgICAgICAgIDxNaWNPZmYgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8TWljIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgIHsvKiBTZW5kIEJ1dHRvbiAqL31cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTdWJtaXR9XG4gICAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWQgfHwgIWlzTWVzc2FnZVZhbGlkfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgJ2ZsZXgtc2hyaW5rLTAgcC0yIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwJyxcbiAgICAgICAgICAgICAgaXNNZXNzYWdlVmFsaWQgJiYgIWRpc2FibGVkXG4gICAgICAgICAgICAgICAgPyAnYmctcHJpbWFyeS02MDAgdGV4dC13aGl0ZSBob3ZlcjpiZy1wcmltYXJ5LTcwMCBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnJ1xuICAgICAgICAgICAgICAgIDogJ2JnLXNsYXRlLTEwMCB0ZXh0LXNsYXRlLTQwMCBjdXJzb3Itbm90LWFsbG93ZWQnXG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgdGl0bGU9XCJTZW5kIG1lc3NhZ2VcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZW5kIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUmVjb3JkaW5nIEluZGljYXRvciAqL31cbiAgICAgICAge2lzUmVjb3JkaW5nICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMTIgbGVmdC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXdoaXRlIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG4gICAgICAgICAgICBSZWNvcmRpbmcuLi5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUXVpY2sgQWN0aW9ucyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG10LTMgdGV4dC14cyB0ZXh0LXNsYXRlLTUwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgPHNwYW4+UHJlc3MgRW50ZXIgdG8gc2VuZCwgU2hpZnQrRW50ZXIgZm9yIG5ldyBsaW5lPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIHtkaXNhYmxlZCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXByaW1hcnktNTAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG4gICAgICAgICAgICA8c3Bhbj5BSSBpcyB0aGlua2luZy4uLjwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUmVmIiwiU2VuZCIsIlBhcGVyY2xpcCIsIk1pYyIsIk1pY09mZiIsImNuIiwiZ2V0TGFuZ3VhZ2VEaXJlY3Rpb24iLCJjb25maWciLCJNZXNzYWdlSW5wdXQiLCJvblNlbmRNZXNzYWdlIiwiZGlzYWJsZWQiLCJsYW5ndWFnZSIsInBsYWNlaG9sZGVyIiwibWVzc2FnZSIsInNldE1lc3NhZ2UiLCJpc1JlY29yZGluZyIsInNldElzUmVjb3JkaW5nIiwidGV4dGFyZWFSZWYiLCJkaXJlY3Rpb24iLCJoYW5kbGVTdWJtaXQiLCJ0cmltIiwiY3VycmVudCIsInN0eWxlIiwiaGVpZ2h0IiwiaGFuZGxlS2V5RG93biIsImUiLCJrZXkiLCJzaGlmdEtleSIsInByZXZlbnREZWZhdWx0IiwiaGFuZGxlSW5wdXRDaGFuZ2UiLCJ2YWx1ZSIsInRhcmdldCIsImxlbmd0aCIsIm1heE1lc3NhZ2VMZW5ndGgiLCJ0ZXh0YXJlYSIsIk1hdGgiLCJtaW4iLCJzY3JvbGxIZWlnaHQiLCJoYW5kbGVGaWxlVXBsb2FkIiwiY29uc29sZSIsImxvZyIsImhhbmRsZVZvaWNlUmVjb3JkaW5nIiwiaXNNZXNzYWdlVmFsaWQiLCJkaXYiLCJjbGFzc05hbWUiLCJkaXIiLCJlbmFibGVGaWxlVXBsb2FkIiwiYnV0dG9uIiwib25DbGljayIsInRpdGxlIiwicmVmIiwib25DaGFuZ2UiLCJvbktleURvd24iLCJyb3dzIiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/MessageInput.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/MessageList.tsx":
/*!*********************************************!*\
  !*** ./src/components/chat/MessageList.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageList: () => (/* binding */ MessageList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MessageBubble */ \"(ssr)/./src/components/chat/MessageBubble.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MessageList auto */ \n\n\nfunction MessageList({ messages, language, onRetry, onCopy }) {\n    const direction = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getLanguageDirection)(language);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-6 ${direction === \"rtl\" ? \"rtl\" : \"ltr\"}`,\n        dir: direction,\n        children: messages.map((message, index)=>{\n            const isLastMessage = index === messages.length - 1;\n            const showTimestamp = index === 0 || index > 0 && new Date(message.timestamp).getTime() - new Date(messages[index - 1].timestamp).getTime() > 300000; // 5 minutes\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-enter\",\n                children: [\n                    showTimestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-slate-500 bg-white px-3 py-1 rounded-full shadow-sm\",\n                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatTimestamp)(message.timestamp)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex ${message.role === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_1__.MessageBubble, {\n                            message: message,\n                            language: language,\n                            onRetry: onRetry,\n                            onCopy: onCopy,\n                            showActions: isLastMessage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, message.id, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                lineNumber: 26,\n                columnNumber: 11\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/MessageList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/TypingIndicator.tsx":
/*!*************************************************!*\
  !*** ./src/components/chat/TypingIndicator.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TypingIndicator: () => (/* binding */ TypingIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ TypingIndicator auto */ \n\nfunction TypingIndicator() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-start gap-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-primary-600 font-bold text-sm\",\n                    children: \"FS\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\TypingIndicator.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\TypingIndicator.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border border-slate-200 rounded-2xl px-4 py-3 shadow-sm max-w-[200px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs text-slate-500\",\n                            children: [\n                                _lib_config__WEBPACK_IMPORTED_MODULE_1__.config.companyName,\n                                \" Assistant\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\TypingIndicator.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\TypingIndicator.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"typing-indicator\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"typing-dot\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\TypingIndicator.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"typing-dot\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\TypingIndicator.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"typing-dot\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\TypingIndicator.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\TypingIndicator.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\TypingIndicator.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\TypingIndicator.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/TypingIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/WelcomeScreen.tsx":
/*!***********************************************!*\
  !*** ./src/components/chat/WelcomeScreen.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WelcomeScreen: () => (/* binding */ WelcomeScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_MessageSquare_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,MessageSquare,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_MessageSquare_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,MessageSquare,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_MessageSquare_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,MessageSquare,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_MessageSquare_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,MessageSquare,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ WelcomeScreen auto */ \n\n\nfunction WelcomeScreen({ onStartChat, language }) {\n    const messages = _lib_config__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_MESSAGES[language];\n    const features = [\n        {\n            icon: _barrel_optimize_names_Brain_FileText_MessageSquare_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: language === \"en\" ? \"Company Knowledge\" : language === \"fr\" ? \"Connaissances de l'entreprise\" : \"معرفة الشركة\",\n            description: language === \"en\" ? \"Ask about Flexible Soft services, policies, and internal documentation\" : language === \"fr\" ? \"Posez des questions sur les services, politiques et documentation interne de Flexible Soft\" : \"اسأل عن خدمات وسياسات ووثائق Flexible Soft الداخلية\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_FileText_MessageSquare_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: language === \"en\" ? \"Technical Expertise\" : language === \"fr\" ? \"Expertise technique\" : \"الخبرة التقنية\",\n            description: language === \"en\" ? \"Get expert guidance on programming, AI, and software development\" : language === \"fr\" ? \"Obtenez des conseils d'expert sur la programmation, l'IA et le d\\xe9veloppement logiciel\" : \"احصل على إرشادات خبير في البرمجة والذكاء الاصطناعي وتطوير البرمجيات\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_FileText_MessageSquare_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: language === \"en\" ? \"Document Search\" : language === \"fr\" ? \"Recherche de documents\" : \"البحث في المستندات\",\n            description: language === \"en\" ? \"Search through uploaded company documents and knowledge base\" : language === \"fr\" ? \"Recherchez dans les documents d'entreprise t\\xe9l\\xe9charg\\xe9s et la base de connaissances\" : \"ابحث في مستندات الشركة المرفوعة وقاعدة المعرفة\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_FileText_MessageSquare_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: language === \"en\" ? \"Instant Answers\" : language === \"fr\" ? \"R\\xe9ponses instantan\\xe9es\" : \"إجابات فورية\",\n            description: language === \"en\" ? \"Get quick, accurate responses powered by advanced AI technology\" : language === \"fr\" ? \"Obtenez des r\\xe9ponses rapides et pr\\xe9cises aliment\\xe9es par une technologie IA avanc\\xe9e\" : \"احصل على إجابات سريعة ودقيقة مدعومة بتقنية الذكاء الاصطناعي المتقدمة\"\n        }\n    ];\n    const quickQuestions = language === \"en\" ? [\n        \"What services does Flexible Soft offer?\",\n        \"How do I submit a project request?\",\n        \"What technologies does the team use?\",\n        \"Tell me about React best practices\"\n    ] : language === \"fr\" ? [\n        \"Quels services propose Flexible Soft ?\",\n        \"Comment soumettre une demande de projet ?\",\n        \"Quelles technologies utilise l'\\xe9quipe ?\",\n        \"Parlez-moi des meilleures pratiques React\"\n    ] : [\n        \"ما هي الخدمات التي تقدمها Flexible Soft؟\",\n        \"كيف أقدم طلب مشروع؟\",\n        \"ما هي التقنيات التي يستخدمها الفريق؟\",\n        \"أخبرني عن أفضل ممارسات React\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 overflow-y-auto custom-scrollbar\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-6 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-2xl mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl font-bold text-primary-600\",\n                                children: \"FS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-slate-900 mb-4 gradient-text\",\n                            children: language === \"en\" ? `Welcome to ${_lib_config__WEBPACK_IMPORTED_MODULE_1__.config.companyName}` : language === \"fr\" ? `Bienvenue chez ${_lib_config__WEBPACK_IMPORTED_MODULE_1__.config.companyName}` : `مرحباً بك في ${_lib_config__WEBPACK_IMPORTED_MODULE_1__.config.companyName}`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-slate-600 mb-8 max-w-2xl mx-auto\",\n                            children: language === \"en\" ? \"Your AI-powered knowledge assistant for all things related to software development, company policies, and technical guidance.\" : language === \"fr\" ? \"Votre assistant de connaissances aliment\\xe9 par l'IA pour tout ce qui concerne le d\\xe9veloppement logiciel, les politiques d'entreprise et les conseils techniques.\" : \"مساعدك المعرفي المدعوم بالذكاء الاصطناعي لكل ما يتعلق بتطوير البرمجيات وسياسات الشركة والإرشادات التقنية.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onStartChat,\n                            className: \"btn-primary text-lg px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\",\n                            children: language === \"en\" ? \"Start Chatting\" : language === \"fr\" ? \"Commencer \\xe0 discuter\" : \"ابدأ المحادثة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-6 mb-12\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl p-6 shadow-soft hover:shadow-medium transition-shadow duration-200 border border-slate-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                            className: \"w-6 h-6 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 mb-2\",\n                                                children: feature.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600 leading-relaxed\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl p-6 shadow-soft border border-slate-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-slate-900 mb-4\",\n                            children: language === \"en\" ? \"Try asking:\" : language === \"fr\" ? \"Essayez de demander :\" : \"جرب أن تسأل:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid sm:grid-cols-2 gap-3\",\n                            children: quickQuestions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        onStartChat();\n                                    // TODO: Pre-fill the question in the input\n                                    },\n                                    className: \"text-left p-3 bg-slate-50 hover:bg-slate-100 rounded-lg transition-colors duration-200 text-slate-700 hover:text-slate-900\",\n                                    children: [\n                                        '\"',\n                                        question,\n                                        '\"'\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center gap-2 text-sm text-slate-500 bg-slate-50 px-4 py-2 rounded-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Powered by\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-slate-700\",\n                                    children: _lib_config__WEBPACK_IMPORTED_MODULE_1__.COMPANY_INFO.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: _lib_config__WEBPACK_IMPORTED_MODULE_1__.COMPANY_INFO.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex flex-wrap justify-center gap-2\",\n                            children: _lib_config__WEBPACK_IMPORTED_MODULE_1__.COMPANY_INFO.technologies.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block px-3 py-1 bg-primary-100 text-primary-700 text-xs font-medium rounded-full\",\n                                    children: tech\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\chat\\\\WelcomeScreen.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/WelcomeScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Download_Globe_Menu_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Globe,Menu,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Globe_Menu_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Globe,Menu,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Globe_Menu_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Globe,Menu,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Globe_Menu_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Globe,Menu,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\nfunction Header({ onToggleSidebar, currentSession, language, onLanguageChange }) {\n    const [showLanguageMenu, setShowLanguageMenu] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showSettingsMenu, setShowSettingsMenu] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const handleExportChat = ()=>{\n        if (currentSession && currentSession.messages.length > 0) {\n            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.exportChatHistory)(currentSession.messages, currentSession.id);\n        }\n    };\n    const supportedLanguages = [\n        {\n            code: \"en\",\n            name: \"English\",\n            flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n        },\n        {\n            code: \"fr\",\n            name: \"Fran\\xe7ais\",\n            flag: \"\\uD83C\\uDDEB\\uD83C\\uDDF7\"\n        },\n        {\n            code: \"ar\",\n            name: \"العربية\",\n            flag: \"\\uD83C\\uDDF9\\uD83C\\uDDF3\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white/80 backdrop-blur-sm border-b border-slate-200 px-4 py-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleSidebar,\n                                className: \"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors duration-200 lg:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Globe_Menu_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"FS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-lg font-semibold text-slate-900\",\n                                                children: _lib_config__WEBPACK_IMPORTED_MODULE_1__.config.appName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-slate-500\",\n                                                children: _lib_config__WEBPACK_IMPORTED_MODULE_1__.COMPANY_INFO.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:block flex-1 max-w-md mx-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-sm font-medium text-slate-900 truncate\",\n                                    children: currentSession.title || (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.generateSessionTitle)(currentSession.messages)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-slate-500\",\n                                    children: [\n                                        currentSession.messages.length,\n                                        \" messages\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            currentSession && currentSession.messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExportChat,\n                                className: \"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors duration-200\",\n                                title: \"Export chat history\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Globe_Menu_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            _lib_config__WEBPACK_IMPORTED_MODULE_1__.config.enableMultilingual && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowLanguageMenu(!showLanguageMenu),\n                                        className: \"flex items-center gap-2 p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors duration-200\",\n                                        title: \"Change language\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Globe_Menu_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline text-sm\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getLanguageName)(language)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    showLanguageMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200 py-2 z-50\",\n                                        children: supportedLanguages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    onLanguageChange(lang.code);\n                                                    setShowLanguageMenu(false);\n                                                },\n                                                className: `w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-slate-50 transition-colors duration-200 ${language === lang.code ? \"bg-primary-50 text-primary-700\" : \"text-slate-700\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: lang.flag\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: lang.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    language === lang.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-auto text-primary-600\",\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, lang.code, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowSettingsMenu(!showSettingsMenu),\n                                        className: \"p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors duration-200\",\n                                        title: \"Settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Globe_Menu_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    showSettingsMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 top-full mt-2 w-56 bg-white rounded-lg shadow-lg border border-slate-200 py-2 z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-2 border-b border-slate-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-slate-900\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    // TODO: Open preferences modal\n                                                    setShowSettingsMenu(false);\n                                                },\n                                                className: \"w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors duration-200\",\n                                                children: \"Preferences\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    // TODO: Open about modal\n                                                    setShowSettingsMenu(false);\n                                                },\n                                                className: \"w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors duration-200\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-slate-100 mt-2 pt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-4 py-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-slate-500\",\n                                                            children: \"Version 1.0.0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-slate-500\",\n                                                            children: [\n                                                                \"\\xa9 2024 \",\n                                                                _lib_config__WEBPACK_IMPORTED_MODULE_1__.COMPANY_INFO.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            (showLanguageMenu || showSettingsMenu) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>{\n                    setShowLanguageMenu(false);\n                    setShowSettingsMenu(false);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,MessageSquare,MoreVertical,Plus,Search,Trash2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,MessageSquare,MoreVertical,Plus,Search,Trash2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,MessageSquare,MoreVertical,Plus,Search,Trash2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,MessageSquare,MoreVertical,Plus,Search,Trash2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,MessageSquare,MoreVertical,Plus,Search,Trash2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,MessageSquare,MoreVertical,Plus,Search,Trash2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,MessageSquare,MoreVertical,Plus,Search,Trash2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,MessageSquare,MoreVertical,Plus,Search,Trash2,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\nfunction Sidebar({ isOpen, onClose, sessions, currentSession, onNewSession, onSelectSession, onDeleteSession, onClearAll, language }) {\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showClearConfirm, setShowClearConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sessionMenuOpen, setSessionMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filter sessions based on search query\n    const filteredSessions = sessions.filter((session)=>{\n        const title = session.title || (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.generateSessionTitle)(session.messages);\n        const content = session.messages.map((m)=>m.content).join(\" \");\n        const query = searchQuery.toLowerCase();\n        return title.toLowerCase().includes(query) || content.toLowerCase().includes(query);\n    });\n    const handleDeleteSession = (sessionId)=>{\n        onDeleteSession(sessionId);\n        setSessionMenuOpen(null);\n    };\n    const handleClearAll = ()=>{\n        if (showClearConfirm) {\n            onClearAll();\n            setShowClearConfirm(false);\n        } else {\n            setShowClearConfirm(true);\n        }\n    };\n    const sidebarContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white border-r border-slate-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b border-slate-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-slate-900\",\n                        children: language === \"en\" ? \"Chat History\" : language === \"fr\" ? \"Historique des discussions\" : \"تاريخ المحادثات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"p-1 text-slate-400 hover:text-slate-600 lg:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{\n                        onNewSession();\n                        onClose();\n                    },\n                    className: \"w-full flex items-center gap-3 p-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: language === \"en\" ? \"New Chat\" : language === \"fr\" ? \"Nouvelle discussion\" : \"محادثة جديدة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            placeholder: language === \"en\" ? \"Search conversations...\" : language === \"fr\" ? \"Rechercher des conversations...\" : \"البحث في المحادثات...\",\n                            value: searchQuery,\n                            onChange: (e)=>setSearchQuery(e.target.value),\n                            className: \"w-full pl-10 pr-4 py-2 border border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto custom-scrollbar px-4\",\n                children: filteredSessions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-12 h-12 text-slate-300 mx-auto mb-3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-slate-500 text-sm\",\n                            children: searchQuery ? language === \"en\" ? \"No conversations found\" : language === \"fr\" ? \"Aucune conversation trouv\\xe9e\" : \"لم يتم العثور على محادثات\" : language === \"en\" ? \"No conversations yet\" : language === \"fr\" ? \"Aucune conversation pour le moment\" : \"لا توجد محادثات بعد\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: filteredSessions.map((session)=>{\n                        const title = session.title || (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.generateSessionTitle)(session.messages);\n                        const lastMessage = session.messages[session.messages.length - 1];\n                        const isActive = currentSession?.id === session.id;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `group relative p-3 rounded-lg cursor-pointer transition-all duration-200 ${isActive ? \"bg-primary-50 border border-primary-200\" : \"hover:bg-slate-50 border border-transparent\"}`,\n                            onClick: ()=>{\n                                onSelectSession(session);\n                                onClose();\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: `text-sm font-medium truncate ${isActive ? \"text-primary-900\" : \"text-slate-900\"}`,\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.truncateText)(title, 40)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 23\n                                            }, this),\n                                            lastMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `text-xs mt-1 truncate ${isActive ? \"text-primary-600\" : \"text-slate-500\"}`,\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.truncateText)(lastMessage.content, 50)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `text-xs mt-1 ${isActive ? \"text-primary-500\" : \"text-slate-400\"}`,\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatTimestamp)(session.updated_at)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setSessionMenuOpen(sessionMenuOpen === session.id ? null : session.id);\n                                                },\n                                                className: \"p-1 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 23\n                                            }, this),\n                                            sessionMenuOpen === session.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 top-full mt-1 w-32 bg-white rounded-lg shadow-lg border border-slate-200 py-1 z-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        handleDeleteSession(session.id);\n                                                    },\n                                                    className: \"w-full flex items-center gap-2 px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 transition-colors duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        language === \"en\" ? \"Delete\" : language === \"fr\" ? \"Supprimer\" : \"حذف\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 19\n                            }, this)\n                        }, session.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 17\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-slate-100 space-y-2\",\n                children: [\n                    _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.enableFileUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            // TODO: Open document upload modal\n                            onClose();\n                        },\n                        className: \"w-full flex items-center gap-3 p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: language === \"en\" ? \"Upload Documents\" : language === \"fr\" ? \"T\\xe9l\\xe9charger des documents\" : \"رفع المستندات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            // TODO: Open document management modal\n                            onClose();\n                        },\n                        className: \"w-full flex items-center gap-3 p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50 rounded-lg transition-colors duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: language === \"en\" ? \"Manage Documents\" : language === \"fr\" ? \"G\\xe9rer les documents\" : \"إدارة المستندات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this),\n                    sessions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClearAll,\n                        className: `w-full flex items-center gap-3 p-2 rounded-lg transition-colors duration-200 ${showClearConfirm ? \"text-red-700 bg-red-50 hover:bg-red-100\" : \"text-slate-600 hover:text-red-600 hover:bg-red-50\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_MessageSquare_MoreVertical_Plus_Search_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: showClearConfirm ? language === \"en\" ? \"Confirm Clear All\" : language === \"fr\" ? \"Confirmer tout effacer\" : \"تأكيد مسح الكل\" : language === \"en\" ? \"Clear All Chats\" : language === \"fr\" ? \"Effacer toutes les discussions\" : \"مسح جميع المحادثات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block w-80 h-full\",\n                children: sidebarContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `lg:hidden fixed inset-y-0 left-0 z-50 w-80 transform transition-transform duration-300 ease-in-out ${isOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n                children: sidebarContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            sessionMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setSessionMenuOpen(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 297,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   clearChatHistory: () => (/* binding */ clearChatHistory),\n/* harmony export */   deleteDocument: () => (/* binding */ deleteDocument),\n/* harmony export */   getApiInfo: () => (/* binding */ getApiInfo),\n/* harmony export */   getChatHistory: () => (/* binding */ getChatHistory),\n/* harmony export */   getChatStats: () => (/* binding */ getChatStats),\n/* harmony export */   getDocument: () => (/* binding */ getDocument),\n/* harmony export */   getDocumentCategories: () => (/* binding */ getDocumentCategories),\n/* harmony export */   getDocumentStats: () => (/* binding */ getDocumentStats),\n/* harmony export */   getDocumentTags: () => (/* binding */ getDocumentTags),\n/* harmony export */   getDocuments: () => (/* binding */ getDocuments),\n/* harmony export */   getHealth: () => (/* binding */ getHealth),\n/* harmony export */   getSystemStats: () => (/* binding */ getSystemStats),\n/* harmony export */   reindexDocuments: () => (/* binding */ reindexDocuments),\n/* harmony export */   searchDocuments: () => (/* binding */ searchDocuments),\n/* harmony export */   sendMessage: () => (/* binding */ sendMessage),\n/* harmony export */   sendMessageStream: () => (/* binding */ sendMessageStream),\n/* harmony export */   submitFeedback: () => (/* binding */ submitFeedback),\n/* harmony export */   uploadDocument: () => (/* binding */ uploadDocument)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/config.ts\");\n// API client for Flexible Soft Chatbot\n\n\nclass ApiClient {\n    constructor(){\n        this.client = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n            baseURL: _config__WEBPACK_IMPORTED_MODULE_0__.config.apiUrl,\n            timeout: 30000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        // Request interceptor\n        this.client.interceptors.request.use((config)=>{\n            // Add any auth headers here if needed\n            return config;\n        }, (error)=>{\n            return Promise.reject(error);\n        });\n        // Response interceptor\n        this.client.interceptors.response.use((response)=>response, (error)=>{\n            const apiError = {\n                error: \"API Error\",\n                message: \"An unexpected error occurred\"\n            };\n            if (error.response?.data) {\n                const errorData = error.response.data;\n                apiError.error = errorData.error || \"API Error\";\n                apiError.message = errorData.message || errorData.detail || \"An unexpected error occurred\";\n                apiError.details = errorData;\n            } else if (error.request) {\n                apiError.error = \"Network Error\";\n                apiError.message = \"Unable to connect to the server. Please check your internet connection.\";\n            } else {\n                apiError.message = error.message || \"An unexpected error occurred\";\n            }\n            return Promise.reject(apiError);\n        });\n    }\n    // Chat endpoints\n    async sendMessage(request) {\n        const response = await this.client.post(_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.chat, request);\n        return response.data;\n    }\n    // Streaming chat endpoint\n    async sendMessageStream(request, onChunk, onComplete, onError) {\n        try {\n            const response = await fetch(`${_config__WEBPACK_IMPORTED_MODULE_0__.config.apiUrl}${_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.chat}/stream`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(request)\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const reader = response.body?.getReader();\n            if (!reader) {\n                throw new Error(\"No response body reader available\");\n            }\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) break;\n                const chunk = decoder.decode(value);\n                const lines = chunk.split(\"\\n\");\n                for (const line of lines){\n                    if (line.startsWith(\"data: \")) {\n                        try {\n                            const data = JSON.parse(line.slice(6));\n                            if (data.type === \"chunk\") {\n                                onChunk(data.content);\n                                fullResponse = data.full_content;\n                            } else if (data.type === \"end\") {\n                                onComplete(data.final_content || fullResponse);\n                                return;\n                            } else if (data.type === \"error\") {\n                                onError(data.error);\n                                if (data.fallback_response) {\n                                    onComplete(data.fallback_response);\n                                }\n                                return;\n                            }\n                        } catch (e) {\n                            console.warn(\"Failed to parse streaming data:\", line);\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            onError(error instanceof Error ? error.message : \"Streaming failed\");\n        }\n    }\n    async submitFeedback(request) {\n        const response = await this.client.post(_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.feedback, request);\n        return response.data;\n    }\n    async getChatHistory(sessionId) {\n        const response = await this.client.get(`${_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.chat}/history/${sessionId}`);\n        return response.data;\n    }\n    async clearChatHistory(sessionId) {\n        const response = await this.client.delete(`${_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.chat}/history/${sessionId}`);\n        return response.data;\n    }\n    async getChatStats() {\n        const response = await this.client.get(`${_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.chat}/stats`);\n        return response.data;\n    }\n    // Document endpoints\n    async uploadDocument(file, category, tags, description) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        if (category) formData.append(\"category\", category);\n        if (tags && tags.length > 0) formData.append(\"tags\", tags.join(\",\"));\n        if (description) formData.append(\"description\", description);\n        const response = await this.client.post(_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.upload, formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n        return response.data;\n    }\n    async getDocuments(page = 1, pageSize = 50, category, status) {\n        const params = new URLSearchParams({\n            page: page.toString(),\n            page_size: pageSize.toString()\n        });\n        if (category) params.append(\"category\", category);\n        if (status) params.append(\"status\", status);\n        const response = await this.client.get(`${_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.documents}?${params}`);\n        return response.data;\n    }\n    async getDocument(documentId) {\n        const response = await this.client.get(`${_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.documents}/${documentId}`);\n        return response.data;\n    }\n    async deleteDocument(documentId) {\n        const response = await this.client.delete(`${_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.documents}/${documentId}`);\n        return response.data;\n    }\n    async searchDocuments(query, limit = 10) {\n        const response = await this.client.post(`${_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.documents}/search`, {\n            query,\n            limit\n        });\n        return response.data;\n    }\n    async reindexDocuments(documentIds) {\n        const response = await this.client.post(_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.reindex, {\n            document_ids: documentIds\n        });\n        return response.data;\n    }\n    async getDocumentStats() {\n        const response = await this.client.get(`${_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.documents}/stats`);\n        return response.data;\n    }\n    async getDocumentCategories() {\n        const response = await this.client.get(`${_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.documents}/categories`);\n        return response.data;\n    }\n    async getDocumentTags() {\n        const response = await this.client.get(`${_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.documents}/tags`);\n        return response.data;\n    }\n    // System endpoints\n    async getHealth() {\n        const response = await this.client.get(_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.health);\n        return response.data;\n    }\n    async getApiInfo() {\n        const response = await this.client.get(_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.info);\n        return response.data;\n    }\n    async getSystemStats() {\n        const response = await this.client.get(_config__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.stats);\n        return response.data;\n    }\n}\n// Export singleton instance\nconst apiClient = new ApiClient();\n// Export individual methods for convenience (properly bound)\nconst sendMessage = (request)=>apiClient.sendMessage(request);\nconst sendMessageStream = (request, onChunk, onComplete, onError)=>apiClient.sendMessageStream(request, onChunk, onComplete, onError);\nconst submitFeedback = (request)=>apiClient.submitFeedback(request);\nconst getChatHistory = (sessionId)=>apiClient.getChatHistory(sessionId);\nconst clearChatHistory = (sessionId)=>apiClient.clearChatHistory(sessionId);\nconst getChatStats = ()=>apiClient.getChatStats();\nconst uploadDocument = (file, category, tags, description)=>apiClient.uploadDocument(file, category, tags, description);\nconst getDocuments = (page, pageSize, category, status)=>apiClient.getDocuments(page, pageSize, category, status);\nconst getDocument = (documentId)=>apiClient.getDocument(documentId);\nconst deleteDocument = (documentId)=>apiClient.deleteDocument(documentId);\nconst searchDocuments = (query, limit)=>apiClient.searchDocuments(query, limit);\nconst reindexDocuments = (documentIds)=>apiClient.reindexDocuments(documentIds);\nconst getDocumentStats = ()=>apiClient.getDocumentStats();\nconst getDocumentCategories = ()=>apiClient.getDocumentCategories();\nconst getDocumentTags = ()=>apiClient.getDocumentTags();\nconst getHealth = ()=>apiClient.getHealth();\nconst getApiInfo = ()=>apiClient.getApiInfo();\nconst getSystemStats = ()=>apiClient.getSystemStats();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwaS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSx1Q0FBdUM7QUFDa0I7QUFDUjtBQVlqRCxNQUFNRztJQUdKQyxhQUFjO1FBQ1osSUFBSSxDQUFDQyxNQUFNLEdBQUdMLDZDQUFLQSxDQUFDTSxNQUFNLENBQUM7WUFDekJDLFNBQVNOLDJDQUFNQSxDQUFDTyxNQUFNO1lBQ3RCQyxTQUFTO1lBQ1RDLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1FBQ0Y7UUFFQSxzQkFBc0I7UUFDdEIsSUFBSSxDQUFDTCxNQUFNLENBQUNNLFlBQVksQ0FBQ0MsT0FBTyxDQUFDQyxHQUFHLENBQ2xDLENBQUNaO1lBQ0Msc0NBQXNDO1lBQ3RDLE9BQU9BO1FBQ1QsR0FDQSxDQUFDYTtZQUNDLE9BQU9DLFFBQVFDLE1BQU0sQ0FBQ0Y7UUFDeEI7UUFHRix1QkFBdUI7UUFDdkIsSUFBSSxDQUFDVCxNQUFNLENBQUNNLFlBQVksQ0FBQ00sUUFBUSxDQUFDSixHQUFHLENBQ25DLENBQUNJLFdBQWFBLFVBQ2QsQ0FBQ0g7WUFDQyxNQUFNSSxXQUFxQjtnQkFDekJKLE9BQU87Z0JBQ1BLLFNBQVM7WUFDWDtZQUVBLElBQUlMLE1BQU1HLFFBQVEsRUFBRUcsTUFBTTtnQkFDeEIsTUFBTUMsWUFBWVAsTUFBTUcsUUFBUSxDQUFDRyxJQUFJO2dCQUNyQ0YsU0FBU0osS0FBSyxHQUFHTyxVQUFVUCxLQUFLLElBQUk7Z0JBQ3BDSSxTQUFTQyxPQUFPLEdBQUdFLFVBQVVGLE9BQU8sSUFBSUUsVUFBVUMsTUFBTSxJQUFJO2dCQUM1REosU0FBU0ssT0FBTyxHQUFHRjtZQUNyQixPQUFPLElBQUlQLE1BQU1GLE9BQU8sRUFBRTtnQkFDeEJNLFNBQVNKLEtBQUssR0FBRztnQkFDakJJLFNBQVNDLE9BQU8sR0FBRztZQUNyQixPQUFPO2dCQUNMRCxTQUFTQyxPQUFPLEdBQUdMLE1BQU1LLE9BQU8sSUFBSTtZQUN0QztZQUVBLE9BQU9KLFFBQVFDLE1BQU0sQ0FBQ0U7UUFDeEI7SUFFSjtJQUVBLGlCQUFpQjtJQUNqQixNQUFNTSxZQUFZWixPQUFvQixFQUF5QjtRQUM3RCxNQUFNSyxXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUNvQixJQUFJLENBQWV2QixrREFBYUEsQ0FBQ3dCLElBQUksRUFBRWQ7UUFDMUUsT0FBT0ssU0FBU0csSUFBSTtJQUN0QjtJQUVBLDBCQUEwQjtJQUMxQixNQUFNTyxrQkFDSmYsT0FBb0IsRUFDcEJnQixPQUFnQyxFQUNoQ0MsVUFBMEMsRUFDMUNDLE9BQWdDLEVBQ2pCO1FBQ2YsSUFBSTtZQUNGLE1BQU1iLFdBQVcsTUFBTWMsTUFBTSxDQUFDLEVBQUU5QiwyQ0FBTUEsQ0FBQ08sTUFBTSxDQUFDLEVBQUVOLGtEQUFhQSxDQUFDd0IsSUFBSSxDQUFDLE9BQU8sQ0FBQyxFQUFFO2dCQUMzRU0sUUFBUTtnQkFDUnRCLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQXVCLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ3ZCO1lBQ3ZCO1lBRUEsSUFBSSxDQUFDSyxTQUFTbUIsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU0sQ0FBQyxvQkFBb0IsRUFBRXBCLFNBQVNxQixNQUFNLENBQUMsQ0FBQztZQUMxRDtZQUVBLE1BQU1DLFNBQVN0QixTQUFTZ0IsSUFBSSxFQUFFTztZQUM5QixJQUFJLENBQUNELFFBQVE7Z0JBQ1gsTUFBTSxJQUFJRixNQUFNO1lBQ2xCO1lBRUEsTUFBTUksVUFBVSxJQUFJQztZQUNwQixJQUFJQyxlQUFlO1lBRW5CLE1BQU8sS0FBTTtnQkFDWCxNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTU4sT0FBT08sSUFBSTtnQkFFekMsSUFBSUYsTUFBTTtnQkFFVixNQUFNRyxRQUFRTixRQUFRTyxNQUFNLENBQUNIO2dCQUM3QixNQUFNSSxRQUFRRixNQUFNRyxLQUFLLENBQUM7Z0JBRTFCLEtBQUssTUFBTUMsUUFBUUYsTUFBTztvQkFDeEIsSUFBSUUsS0FBS0MsVUFBVSxDQUFDLFdBQVc7d0JBQzdCLElBQUk7NEJBQ0YsTUFBTWhDLE9BQU9jLEtBQUttQixLQUFLLENBQUNGLEtBQUtHLEtBQUssQ0FBQzs0QkFFbkMsSUFBSWxDLEtBQUttQyxJQUFJLEtBQUssU0FBUztnQ0FDekIzQixRQUFRUixLQUFLb0MsT0FBTztnQ0FDcEJiLGVBQWV2QixLQUFLcUMsWUFBWTs0QkFDbEMsT0FBTyxJQUFJckMsS0FBS21DLElBQUksS0FBSyxPQUFPO2dDQUM5QjFCLFdBQVdULEtBQUtzQyxhQUFhLElBQUlmO2dDQUNqQzs0QkFDRixPQUFPLElBQUl2QixLQUFLbUMsSUFBSSxLQUFLLFNBQVM7Z0NBQ2hDekIsUUFBUVYsS0FBS04sS0FBSztnQ0FDbEIsSUFBSU0sS0FBS3VDLGlCQUFpQixFQUFFO29DQUMxQjlCLFdBQVdULEtBQUt1QyxpQkFBaUI7Z0NBQ25DO2dDQUNBOzRCQUNGO3dCQUNGLEVBQUUsT0FBT0MsR0FBRzs0QkFDVkMsUUFBUUMsSUFBSSxDQUFDLG1DQUFtQ1g7d0JBQ2xEO29CQUNGO2dCQUNGO1lBQ0Y7UUFDRixFQUFFLE9BQU9yQyxPQUFPO1lBQ2RnQixRQUFRaEIsaUJBQWlCdUIsUUFBUXZCLE1BQU1LLE9BQU8sR0FBRztRQUNuRDtJQUNGO0lBRUEsTUFBTTRDLGVBQWVuRCxPQUF3QixFQUFrRDtRQUM3RixNQUFNSyxXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUNvQixJQUFJLENBQUN2QixrREFBYUEsQ0FBQzhELFFBQVEsRUFBRXBEO1FBQ2hFLE9BQU9LLFNBQVNHLElBQUk7SUFDdEI7SUFFQSxNQUFNNkMsZUFBZUMsU0FBaUIsRUFBb0Q7UUFDeEYsTUFBTWpELFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQzhELEdBQUcsQ0FBQyxDQUFDLEVBQUVqRSxrREFBYUEsQ0FBQ3dCLElBQUksQ0FBQyxTQUFTLEVBQUV3QyxVQUFVLENBQUM7UUFDbkYsT0FBT2pELFNBQVNHLElBQUk7SUFDdEI7SUFFQSxNQUFNZ0QsaUJBQWlCRixTQUFpQixFQUFnQztRQUN0RSxNQUFNakQsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDZ0UsTUFBTSxDQUFDLENBQUMsRUFBRW5FLGtEQUFhQSxDQUFDd0IsSUFBSSxDQUFDLFNBQVMsRUFBRXdDLFVBQVUsQ0FBQztRQUN0RixPQUFPakQsU0FBU0csSUFBSTtJQUN0QjtJQUVBLE1BQU1rRCxlQUFtQztRQUN2QyxNQUFNckQsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDOEQsR0FBRyxDQUFZLENBQUMsRUFBRWpFLGtEQUFhQSxDQUFDd0IsSUFBSSxDQUFDLE1BQU0sQ0FBQztRQUMvRSxPQUFPVCxTQUFTRyxJQUFJO0lBQ3RCO0lBRUEscUJBQXFCO0lBQ3JCLE1BQU1tRCxlQUNKQyxJQUFVLEVBQ1ZDLFFBQWlCLEVBQ2pCQyxJQUFlLEVBQ2ZDLFdBQW9CLEVBQ2E7UUFDakMsTUFBTUMsV0FBVyxJQUFJQztRQUNyQkQsU0FBU0UsTUFBTSxDQUFDLFFBQVFOO1FBRXhCLElBQUlDLFVBQVVHLFNBQVNFLE1BQU0sQ0FBQyxZQUFZTDtRQUMxQyxJQUFJQyxRQUFRQSxLQUFLSyxNQUFNLEdBQUcsR0FBR0gsU0FBU0UsTUFBTSxDQUFDLFFBQVFKLEtBQUtNLElBQUksQ0FBQztRQUMvRCxJQUFJTCxhQUFhQyxTQUFTRSxNQUFNLENBQUMsZUFBZUg7UUFFaEQsTUFBTTFELFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQ29CLElBQUksQ0FDckN2QixrREFBYUEsQ0FBQytFLE1BQU0sRUFDcEJMLFVBQ0E7WUFDRWxFLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1FBQ0Y7UUFFRixPQUFPTyxTQUFTRyxJQUFJO0lBQ3RCO0lBRUEsTUFBTThELGFBQ0pDLE9BQU8sQ0FBQyxFQUNSQyxXQUFXLEVBQUUsRUFDYlgsUUFBaUIsRUFDakJuQyxNQUFlLEVBTWQ7UUFDRCxNQUFNK0MsU0FBUyxJQUFJQyxnQkFBZ0I7WUFDakNILE1BQU1BLEtBQUtJLFFBQVE7WUFDbkJDLFdBQVdKLFNBQVNHLFFBQVE7UUFDOUI7UUFFQSxJQUFJZCxVQUFVWSxPQUFPUCxNQUFNLENBQUMsWUFBWUw7UUFDeEMsSUFBSW5DLFFBQVErQyxPQUFPUCxNQUFNLENBQUMsVUFBVXhDO1FBRXBDLE1BQU1yQixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUM4RCxHQUFHLENBQUMsQ0FBQyxFQUFFakUsa0RBQWFBLENBQUN1RixTQUFTLENBQUMsQ0FBQyxFQUFFSixPQUFPLENBQUM7UUFDN0UsT0FBT3BFLFNBQVNHLElBQUk7SUFDdEI7SUFFQSxNQUFNc0UsWUFBWUMsVUFBa0IsRUFBcUI7UUFDdkQsTUFBTTFFLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQzhELEdBQUcsQ0FBVyxDQUFDLEVBQUVqRSxrREFBYUEsQ0FBQ3VGLFNBQVMsQ0FBQyxDQUFDLEVBQUVFLFdBQVcsQ0FBQztRQUMzRixPQUFPMUUsU0FBU0csSUFBSTtJQUN0QjtJQUVBLE1BQU13RSxlQUFlRCxVQUFrQixFQUFnQztRQUNyRSxNQUFNMUUsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDZ0UsTUFBTSxDQUFDLENBQUMsRUFBRW5FLGtEQUFhQSxDQUFDdUYsU0FBUyxDQUFDLENBQUMsRUFBRUUsV0FBVyxDQUFDO1FBQ3BGLE9BQU8xRSxTQUFTRyxJQUFJO0lBQ3RCO0lBRUEsTUFBTXlFLGdCQUFnQkMsS0FBYSxFQUFFQyxRQUFRLEVBQUUsRUFLNUM7UUFDRCxNQUFNOUUsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDb0IsSUFBSSxDQUFDLENBQUMsRUFBRXZCLGtEQUFhQSxDQUFDdUYsU0FBUyxDQUFDLE9BQU8sQ0FBQyxFQUFFO1lBQzNFSztZQUNBQztRQUNGO1FBQ0EsT0FBTzlFLFNBQVNHLElBQUk7SUFDdEI7SUFFQSxNQUFNNEUsaUJBQWlCQyxXQUFzQixFQU0xQztRQUNELE1BQU1oRixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUNvQixJQUFJLENBQUN2QixrREFBYUEsQ0FBQ2dHLE9BQU8sRUFBRTtZQUM3REMsY0FBY0Y7UUFDaEI7UUFDQSxPQUFPaEYsU0FBU0csSUFBSTtJQUN0QjtJQUVBLE1BQU1nRixtQkFBaUM7UUFDckMsTUFBTW5GLFdBQVcsTUFBTSxJQUFJLENBQUNaLE1BQU0sQ0FBQzhELEdBQUcsQ0FBQyxDQUFDLEVBQUVqRSxrREFBYUEsQ0FBQ3VGLFNBQVMsQ0FBQyxNQUFNLENBQUM7UUFDekUsT0FBT3hFLFNBQVNHLElBQUk7SUFDdEI7SUFFQSxNQUFNaUYsd0JBQTJEO1FBQy9ELE1BQU1wRixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUM4RCxHQUFHLENBQUMsQ0FBQyxFQUFFakUsa0RBQWFBLENBQUN1RixTQUFTLENBQUMsV0FBVyxDQUFDO1FBQzlFLE9BQU94RSxTQUFTRyxJQUFJO0lBQ3RCO0lBRUEsTUFBTWtGLGtCQUErQztRQUNuRCxNQUFNckYsV0FBVyxNQUFNLElBQUksQ0FBQ1osTUFBTSxDQUFDOEQsR0FBRyxDQUFDLENBQUMsRUFBRWpFLGtEQUFhQSxDQUFDdUYsU0FBUyxDQUFDLEtBQUssQ0FBQztRQUN4RSxPQUFPeEUsU0FBU0csSUFBSTtJQUN0QjtJQUVBLG1CQUFtQjtJQUNuQixNQUFNbUYsWUFJSDtRQUNELE1BQU10RixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUM4RCxHQUFHLENBQUNqRSxrREFBYUEsQ0FBQ3NHLE1BQU07UUFDM0QsT0FBT3ZGLFNBQVNHLElBQUk7SUFDdEI7SUFFQSxNQUFNcUYsYUFLSDtRQUNELE1BQU14RixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUM4RCxHQUFHLENBQUNqRSxrREFBYUEsQ0FBQ3dHLElBQUk7UUFDekQsT0FBT3pGLFNBQVNHLElBQUk7SUFDdEI7SUFFQSxNQUFNdUYsaUJBQXVDO1FBQzNDLE1BQU0xRixXQUFXLE1BQU0sSUFBSSxDQUFDWixNQUFNLENBQUM4RCxHQUFHLENBQWNqRSxrREFBYUEsQ0FBQzBHLEtBQUs7UUFDdkUsT0FBTzNGLFNBQVNHLElBQUk7SUFDdEI7QUFDRjtBQUVBLDRCQUE0QjtBQUNyQixNQUFNeUYsWUFBWSxJQUFJMUcsWUFBWTtBQUV6Qyw2REFBNkQ7QUFDdEQsTUFBTXFCLGNBQWMsQ0FBQ1osVUFBeUJpRyxVQUFVckYsV0FBVyxDQUFDWixTQUFTO0FBQzdFLE1BQU1lLG9CQUFvQixDQUMvQmYsU0FDQWdCLFNBQ0FDLFlBQ0FDLFVBQ0crRSxVQUFVbEYsaUJBQWlCLENBQUNmLFNBQVNnQixTQUFTQyxZQUFZQyxTQUFTO0FBQ2pFLE1BQU1pQyxpQkFBaUIsQ0FBQ25ELFVBQTZCaUcsVUFBVTlDLGNBQWMsQ0FBQ25ELFNBQVM7QUFDdkYsTUFBTXFELGlCQUFpQixDQUFDQyxZQUFzQjJDLFVBQVU1QyxjQUFjLENBQUNDLFdBQVc7QUFDbEYsTUFBTUUsbUJBQW1CLENBQUNGLFlBQXNCMkMsVUFBVXpDLGdCQUFnQixDQUFDRixXQUFXO0FBQ3RGLE1BQU1JLGVBQWUsSUFBTXVDLFVBQVV2QyxZQUFZLEdBQUc7QUFDcEQsTUFBTUMsaUJBQWlCLENBQUNDLE1BQVlDLFVBQW1CQyxNQUFpQkMsY0FDN0VrQyxVQUFVdEMsY0FBYyxDQUFDQyxNQUFNQyxVQUFVQyxNQUFNQyxhQUFhO0FBQ3ZELE1BQU1PLGVBQWUsQ0FBQ0MsTUFBZUMsVUFBbUJYLFVBQW1CbkMsU0FDaEZ1RSxVQUFVM0IsWUFBWSxDQUFDQyxNQUFNQyxVQUFVWCxVQUFVbkMsUUFBUTtBQUNwRCxNQUFNb0QsY0FBYyxDQUFDQyxhQUF1QmtCLFVBQVVuQixXQUFXLENBQUNDLFlBQVk7QUFDOUUsTUFBTUMsaUJBQWlCLENBQUNELGFBQXVCa0IsVUFBVWpCLGNBQWMsQ0FBQ0QsWUFBWTtBQUNwRixNQUFNRSxrQkFBa0IsQ0FBQ0MsT0FBZUMsUUFBbUJjLFVBQVVoQixlQUFlLENBQUNDLE9BQU9DLE9BQU87QUFDbkcsTUFBTUMsbUJBQW1CLENBQUNDLGNBQTJCWSxVQUFVYixnQkFBZ0IsQ0FBQ0MsYUFBYTtBQUM3RixNQUFNRyxtQkFBbUIsSUFBTVMsVUFBVVQsZ0JBQWdCLEdBQUc7QUFDNUQsTUFBTUMsd0JBQXdCLElBQU1RLFVBQVVSLHFCQUFxQixHQUFHO0FBQ3RFLE1BQU1DLGtCQUFrQixJQUFNTyxVQUFVUCxlQUFlLEdBQUc7QUFDMUQsTUFBTUMsWUFBWSxJQUFNTSxVQUFVTixTQUFTLEdBQUc7QUFDOUMsTUFBTUUsYUFBYSxJQUFNSSxVQUFVSixVQUFVLEdBQUc7QUFDaEQsTUFBTUUsaUJBQWlCLElBQU1FLFVBQVVGLGNBQWMsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZsZXhpYmxlLXNvZnQtY2hhdGJvdC1mcm9udGVuZC8uL3NyYy9saWIvYXBpLnRzPzJmYWIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQVBJIGNsaWVudCBmb3IgRmxleGlibGUgU29mdCBDaGF0Ym90XG5pbXBvcnQgYXhpb3MsIHsgQXhpb3NJbnN0YW5jZSwgQXhpb3NFcnJvciB9IGZyb20gJ2F4aW9zJztcbmltcG9ydCB7IGNvbmZpZywgQVBJX0VORFBPSU5UUyB9IGZyb20gJy4vY29uZmlnJztcbmltcG9ydCB7XG4gIENoYXRSZXF1ZXN0LFxuICBDaGF0UmVzcG9uc2UsXG4gIERvY3VtZW50LFxuICBEb2N1bWVudFVwbG9hZFJlc3BvbnNlLFxuICBGZWVkYmFja1JlcXVlc3QsXG4gIFN5c3RlbVN0YXRzLFxuICBDaGF0U3RhdHMsXG4gIEFwaUVycm9yLFxufSBmcm9tICdAL3R5cGVzJztcblxuY2xhc3MgQXBpQ2xpZW50IHtcbiAgcHJpdmF0ZSBjbGllbnQ6IEF4aW9zSW5zdGFuY2U7XG5cbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5jbGllbnQgPSBheGlvcy5jcmVhdGUoe1xuICAgICAgYmFzZVVSTDogY29uZmlnLmFwaVVybCxcbiAgICAgIHRpbWVvdXQ6IDMwMDAwLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIC8vIFJlcXVlc3QgaW50ZXJjZXB0b3JcbiAgICB0aGlzLmNsaWVudC5pbnRlcmNlcHRvcnMucmVxdWVzdC51c2UoXG4gICAgICAoY29uZmlnKSA9PiB7XG4gICAgICAgIC8vIEFkZCBhbnkgYXV0aCBoZWFkZXJzIGhlcmUgaWYgbmVlZGVkXG4gICAgICAgIHJldHVybiBjb25maWc7XG4gICAgICB9LFxuICAgICAgKGVycm9yKSA9PiB7XG4gICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7XG4gICAgICB9XG4gICAgKTtcblxuICAgIC8vIFJlc3BvbnNlIGludGVyY2VwdG9yXG4gICAgdGhpcy5jbGllbnQuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShcbiAgICAgIChyZXNwb25zZSkgPT4gcmVzcG9uc2UsXG4gICAgICAoZXJyb3I6IEF4aW9zRXJyb3IpID0+IHtcbiAgICAgICAgY29uc3QgYXBpRXJyb3I6IEFwaUVycm9yID0ge1xuICAgICAgICAgIGVycm9yOiAnQVBJIEVycm9yJyxcbiAgICAgICAgICBtZXNzYWdlOiAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcsXG4gICAgICAgIH07XG5cbiAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5kYXRhKSB7XG4gICAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gZXJyb3IucmVzcG9uc2UuZGF0YSBhcyBhbnk7XG4gICAgICAgICAgYXBpRXJyb3IuZXJyb3IgPSBlcnJvckRhdGEuZXJyb3IgfHwgJ0FQSSBFcnJvcic7XG4gICAgICAgICAgYXBpRXJyb3IubWVzc2FnZSA9IGVycm9yRGF0YS5tZXNzYWdlIHx8IGVycm9yRGF0YS5kZXRhaWwgfHwgJ0FuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQnO1xuICAgICAgICAgIGFwaUVycm9yLmRldGFpbHMgPSBlcnJvckRhdGE7XG4gICAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVxdWVzdCkge1xuICAgICAgICAgIGFwaUVycm9yLmVycm9yID0gJ05ldHdvcmsgRXJyb3InO1xuICAgICAgICAgIGFwaUVycm9yLm1lc3NhZ2UgPSAnVW5hYmxlIHRvIGNvbm5lY3QgdG8gdGhlIHNlcnZlci4gUGxlYXNlIGNoZWNrIHlvdXIgaW50ZXJuZXQgY29ubmVjdGlvbi4nO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGFwaUVycm9yLm1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlIHx8ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkJztcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChhcGlFcnJvcik7XG4gICAgICB9XG4gICAgKTtcbiAgfVxuXG4gIC8vIENoYXQgZW5kcG9pbnRzXG4gIGFzeW5jIHNlbmRNZXNzYWdlKHJlcXVlc3Q6IENoYXRSZXF1ZXN0KTogUHJvbWlzZTxDaGF0UmVzcG9uc2U+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LnBvc3Q8Q2hhdFJlc3BvbnNlPihBUElfRU5EUE9JTlRTLmNoYXQsIHJlcXVlc3QpO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgLy8gU3RyZWFtaW5nIGNoYXQgZW5kcG9pbnRcbiAgYXN5bmMgc2VuZE1lc3NhZ2VTdHJlYW0oXG4gICAgcmVxdWVzdDogQ2hhdFJlcXVlc3QsXG4gICAgb25DaHVuazogKGNodW5rOiBzdHJpbmcpID0+IHZvaWQsXG4gICAgb25Db21wbGV0ZTogKGZ1bGxSZXNwb25zZTogc3RyaW5nKSA9PiB2b2lkLFxuICAgIG9uRXJyb3I6IChlcnJvcjogc3RyaW5nKSA9PiB2b2lkXG4gICk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2NvbmZpZy5hcGlVcmx9JHtBUElfRU5EUE9JTlRTLmNoYXR9L3N0cmVhbWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShyZXF1ZXN0KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgSFRUUCBlcnJvciEgc3RhdHVzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVhZGVyID0gcmVzcG9uc2UuYm9keT8uZ2V0UmVhZGVyKCk7XG4gICAgICBpZiAoIXJlYWRlcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ05vIHJlc3BvbnNlIGJvZHkgcmVhZGVyIGF2YWlsYWJsZScpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkZWNvZGVyID0gbmV3IFRleHREZWNvZGVyKCk7XG4gICAgICBsZXQgZnVsbFJlc3BvbnNlID0gJyc7XG5cbiAgICAgIHdoaWxlICh0cnVlKSB7XG4gICAgICAgIGNvbnN0IHsgZG9uZSwgdmFsdWUgfSA9IGF3YWl0IHJlYWRlci5yZWFkKCk7XG5cbiAgICAgICAgaWYgKGRvbmUpIGJyZWFrO1xuXG4gICAgICAgIGNvbnN0IGNodW5rID0gZGVjb2Rlci5kZWNvZGUodmFsdWUpO1xuICAgICAgICBjb25zdCBsaW5lcyA9IGNodW5rLnNwbGl0KCdcXG4nKTtcblxuICAgICAgICBmb3IgKGNvbnN0IGxpbmUgb2YgbGluZXMpIHtcbiAgICAgICAgICBpZiAobGluZS5zdGFydHNXaXRoKCdkYXRhOiAnKSkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IEpTT04ucGFyc2UobGluZS5zbGljZSg2KSk7XG5cbiAgICAgICAgICAgICAgaWYgKGRhdGEudHlwZSA9PT0gJ2NodW5rJykge1xuICAgICAgICAgICAgICAgIG9uQ2h1bmsoZGF0YS5jb250ZW50KTtcbiAgICAgICAgICAgICAgICBmdWxsUmVzcG9uc2UgPSBkYXRhLmZ1bGxfY29udGVudDtcbiAgICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09ICdlbmQnKSB7XG4gICAgICAgICAgICAgICAgb25Db21wbGV0ZShkYXRhLmZpbmFsX2NvbnRlbnQgfHwgZnVsbFJlc3BvbnNlKTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YS50eXBlID09PSAnZXJyb3InKSB7XG4gICAgICAgICAgICAgICAgb25FcnJvcihkYXRhLmVycm9yKTtcbiAgICAgICAgICAgICAgICBpZiAoZGF0YS5mYWxsYmFja19yZXNwb25zZSkge1xuICAgICAgICAgICAgICAgICAgb25Db21wbGV0ZShkYXRhLmZhbGxiYWNrX3Jlc3BvbnNlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIHBhcnNlIHN0cmVhbWluZyBkYXRhOicsIGxpbmUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBvbkVycm9yKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1N0cmVhbWluZyBmYWlsZWQnKTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBzdWJtaXRGZWVkYmFjayhyZXF1ZXN0OiBGZWVkYmFja1JlcXVlc3QpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgbWVzc2FnZTogc3RyaW5nIH0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LnBvc3QoQVBJX0VORFBPSU5UUy5mZWVkYmFjaywgcmVxdWVzdCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICBhc3luYyBnZXRDaGF0SGlzdG9yeShzZXNzaW9uSWQ6IHN0cmluZyk6IFByb21pc2U8eyBzZXNzaW9uX2lkOiBzdHJpbmc7IG1lc3NhZ2VzOiBhbnlbXSB9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoYCR7QVBJX0VORFBPSU5UUy5jaGF0fS9oaXN0b3J5LyR7c2Vzc2lvbklkfWApO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgY2xlYXJDaGF0SGlzdG9yeShzZXNzaW9uSWQ6IHN0cmluZyk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmcgfT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuZGVsZXRlKGAke0FQSV9FTkRQT0lOVFMuY2hhdH0vaGlzdG9yeS8ke3Nlc3Npb25JZH1gKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuXG4gIGFzeW5jIGdldENoYXRTdGF0cygpOiBQcm9taXNlPENoYXRTdGF0cz4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0PENoYXRTdGF0cz4oYCR7QVBJX0VORFBPSU5UUy5jaGF0fS9zdGF0c2ApO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgLy8gRG9jdW1lbnQgZW5kcG9pbnRzXG4gIGFzeW5jIHVwbG9hZERvY3VtZW50KFxuICAgIGZpbGU6IEZpbGUsXG4gICAgY2F0ZWdvcnk/OiBzdHJpbmcsXG4gICAgdGFncz86IHN0cmluZ1tdLFxuICAgIGRlc2NyaXB0aW9uPzogc3RyaW5nXG4gICk6IFByb21pc2U8RG9jdW1lbnRVcGxvYWRSZXNwb25zZT4ge1xuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XG4gICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSk7XG4gICAgXG4gICAgaWYgKGNhdGVnb3J5KSBmb3JtRGF0YS5hcHBlbmQoJ2NhdGVnb3J5JywgY2F0ZWdvcnkpO1xuICAgIGlmICh0YWdzICYmIHRhZ3MubGVuZ3RoID4gMCkgZm9ybURhdGEuYXBwZW5kKCd0YWdzJywgdGFncy5qb2luKCcsJykpO1xuICAgIGlmIChkZXNjcmlwdGlvbikgZm9ybURhdGEuYXBwZW5kKCdkZXNjcmlwdGlvbicsIGRlc2NyaXB0aW9uKTtcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQucG9zdDxEb2N1bWVudFVwbG9hZFJlc3BvbnNlPihcbiAgICAgIEFQSV9FTkRQT0lOVFMudXBsb2FkLFxuICAgICAgZm9ybURhdGEsXG4gICAgICB7XG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ211bHRpcGFydC9mb3JtLWRhdGEnLFxuICAgICAgICB9LFxuICAgICAgfVxuICAgICk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICBhc3luYyBnZXREb2N1bWVudHMoXG4gICAgcGFnZSA9IDEsXG4gICAgcGFnZVNpemUgPSA1MCxcbiAgICBjYXRlZ29yeT86IHN0cmluZyxcbiAgICBzdGF0dXM/OiBzdHJpbmdcbiAgKTogUHJvbWlzZTx7XG4gICAgZG9jdW1lbnRzOiBEb2N1bWVudFtdO1xuICAgIHRvdGFsX2NvdW50OiBudW1iZXI7XG4gICAgcGFnZTogbnVtYmVyO1xuICAgIHBhZ2Vfc2l6ZTogbnVtYmVyO1xuICB9PiB7XG4gICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh7XG4gICAgICBwYWdlOiBwYWdlLnRvU3RyaW5nKCksXG4gICAgICBwYWdlX3NpemU6IHBhZ2VTaXplLnRvU3RyaW5nKCksXG4gICAgfSk7XG5cbiAgICBpZiAoY2F0ZWdvcnkpIHBhcmFtcy5hcHBlbmQoJ2NhdGVnb3J5JywgY2F0ZWdvcnkpO1xuICAgIGlmIChzdGF0dXMpIHBhcmFtcy5hcHBlbmQoJ3N0YXR1cycsIHN0YXR1cyk7XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChgJHtBUElfRU5EUE9JTlRTLmRvY3VtZW50c30/JHtwYXJhbXN9YCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICBhc3luYyBnZXREb2N1bWVudChkb2N1bWVudElkOiBzdHJpbmcpOiBQcm9taXNlPERvY3VtZW50PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQ8RG9jdW1lbnQ+KGAke0FQSV9FTkRQT0lOVFMuZG9jdW1lbnRzfS8ke2RvY3VtZW50SWR9YCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICBhc3luYyBkZWxldGVEb2N1bWVudChkb2N1bWVudElkOiBzdHJpbmcpOiBQcm9taXNlPHsgbWVzc2FnZTogc3RyaW5nIH0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LmRlbGV0ZShgJHtBUElfRU5EUE9JTlRTLmRvY3VtZW50c30vJHtkb2N1bWVudElkfWApO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgc2VhcmNoRG9jdW1lbnRzKHF1ZXJ5OiBzdHJpbmcsIGxpbWl0ID0gMTApOiBQcm9taXNlPHtcbiAgICByZXN1bHRzOiBhbnlbXTtcbiAgICB0b3RhbF9yZXN1bHRzOiBudW1iZXI7XG4gICAgcXVlcnk6IHN0cmluZztcbiAgICBwcm9jZXNzaW5nX3RpbWU6IG51bWJlcjtcbiAgfT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQucG9zdChgJHtBUElfRU5EUE9JTlRTLmRvY3VtZW50c30vc2VhcmNoYCwge1xuICAgICAgcXVlcnksXG4gICAgICBsaW1pdCxcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuXG4gIGFzeW5jIHJlaW5kZXhEb2N1bWVudHMoZG9jdW1lbnRJZHM/OiBzdHJpbmdbXSk6IFByb21pc2U8e1xuICAgIHN1Y2Nlc3M6IGJvb2xlYW47XG4gICAgbWVzc2FnZTogc3RyaW5nO1xuICAgIHByb2Nlc3NlZF9kb2N1bWVudHM6IG51bWJlcjtcbiAgICBmYWlsZWRfZG9jdW1lbnRzOiBudW1iZXI7XG4gICAgcHJvY2Vzc2luZ190aW1lOiBudW1iZXI7XG4gIH0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LnBvc3QoQVBJX0VORFBPSU5UUy5yZWluZGV4LCB7XG4gICAgICBkb2N1bWVudF9pZHM6IGRvY3VtZW50SWRzLFxuICAgIH0pO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgZ2V0RG9jdW1lbnRTdGF0cygpOiBQcm9taXNlPGFueT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KGAke0FQSV9FTkRQT0lOVFMuZG9jdW1lbnRzfS9zdGF0c2ApO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG5cbiAgYXN5bmMgZ2V0RG9jdW1lbnRDYXRlZ29yaWVzKCk6IFByb21pc2U8eyBjYXRlZ29yaWVzOiBzdHJpbmdbXSB9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoYCR7QVBJX0VORFBPSU5UUy5kb2N1bWVudHN9L2NhdGVnb3JpZXNgKTtcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgfVxuXG4gIGFzeW5jIGdldERvY3VtZW50VGFncygpOiBQcm9taXNlPHsgdGFnczogc3RyaW5nW10gfT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KGAke0FQSV9FTkRQT0lOVFMuZG9jdW1lbnRzfS90YWdzYCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICAvLyBTeXN0ZW0gZW5kcG9pbnRzXG4gIGFzeW5jIGdldEhlYWx0aCgpOiBQcm9taXNlPHtcbiAgICBzdGF0dXM6IHN0cmluZztcbiAgICBzZXJ2aWNlczogUmVjb3JkPHN0cmluZywgYm9vbGVhbj47XG4gICAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIH0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChBUElfRU5EUE9JTlRTLmhlYWx0aCk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICBhc3luYyBnZXRBcGlJbmZvKCk6IFByb21pc2U8e1xuICAgIGNvbXBhbnk6IGFueTtcbiAgICBmZWF0dXJlczogYW55O1xuICAgIGxpbWl0czogYW55O1xuICAgIG1vZGVsX2luZm86IGFueTtcbiAgfT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KEFQSV9FTkRQT0lOVFMuaW5mbyk7XG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG4gIH1cblxuICBhc3luYyBnZXRTeXN0ZW1TdGF0cygpOiBQcm9taXNlPFN5c3RlbVN0YXRzPiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQ8U3lzdGVtU3RhdHM+KEFQSV9FTkRQT0lOVFMuc3RhdHMpO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9XG59XG5cbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCBhcGlDbGllbnQgPSBuZXcgQXBpQ2xpZW50KCk7XG5cbi8vIEV4cG9ydCBpbmRpdmlkdWFsIG1ldGhvZHMgZm9yIGNvbnZlbmllbmNlIChwcm9wZXJseSBib3VuZClcbmV4cG9ydCBjb25zdCBzZW5kTWVzc2FnZSA9IChyZXF1ZXN0OiBDaGF0UmVxdWVzdCkgPT4gYXBpQ2xpZW50LnNlbmRNZXNzYWdlKHJlcXVlc3QpO1xuZXhwb3J0IGNvbnN0IHNlbmRNZXNzYWdlU3RyZWFtID0gKFxuICByZXF1ZXN0OiBDaGF0UmVxdWVzdCxcbiAgb25DaHVuazogKGNodW5rOiBzdHJpbmcpID0+IHZvaWQsXG4gIG9uQ29tcGxldGU6IChmdWxsUmVzcG9uc2U6IHN0cmluZykgPT4gdm9pZCxcbiAgb25FcnJvcjogKGVycm9yOiBzdHJpbmcpID0+IHZvaWRcbikgPT4gYXBpQ2xpZW50LnNlbmRNZXNzYWdlU3RyZWFtKHJlcXVlc3QsIG9uQ2h1bmssIG9uQ29tcGxldGUsIG9uRXJyb3IpO1xuZXhwb3J0IGNvbnN0IHN1Ym1pdEZlZWRiYWNrID0gKHJlcXVlc3Q6IEZlZWRiYWNrUmVxdWVzdCkgPT4gYXBpQ2xpZW50LnN1Ym1pdEZlZWRiYWNrKHJlcXVlc3QpO1xuZXhwb3J0IGNvbnN0IGdldENoYXRIaXN0b3J5ID0gKHNlc3Npb25JZDogc3RyaW5nKSA9PiBhcGlDbGllbnQuZ2V0Q2hhdEhpc3Rvcnkoc2Vzc2lvbklkKTtcbmV4cG9ydCBjb25zdCBjbGVhckNoYXRIaXN0b3J5ID0gKHNlc3Npb25JZDogc3RyaW5nKSA9PiBhcGlDbGllbnQuY2xlYXJDaGF0SGlzdG9yeShzZXNzaW9uSWQpO1xuZXhwb3J0IGNvbnN0IGdldENoYXRTdGF0cyA9ICgpID0+IGFwaUNsaWVudC5nZXRDaGF0U3RhdHMoKTtcbmV4cG9ydCBjb25zdCB1cGxvYWREb2N1bWVudCA9IChmaWxlOiBGaWxlLCBjYXRlZ29yeT86IHN0cmluZywgdGFncz86IHN0cmluZ1tdLCBkZXNjcmlwdGlvbj86IHN0cmluZykgPT5cbiAgYXBpQ2xpZW50LnVwbG9hZERvY3VtZW50KGZpbGUsIGNhdGVnb3J5LCB0YWdzLCBkZXNjcmlwdGlvbik7XG5leHBvcnQgY29uc3QgZ2V0RG9jdW1lbnRzID0gKHBhZ2U/OiBudW1iZXIsIHBhZ2VTaXplPzogbnVtYmVyLCBjYXRlZ29yeT86IHN0cmluZywgc3RhdHVzPzogc3RyaW5nKSA9PlxuICBhcGlDbGllbnQuZ2V0RG9jdW1lbnRzKHBhZ2UsIHBhZ2VTaXplLCBjYXRlZ29yeSwgc3RhdHVzKTtcbmV4cG9ydCBjb25zdCBnZXREb2N1bWVudCA9IChkb2N1bWVudElkOiBzdHJpbmcpID0+IGFwaUNsaWVudC5nZXREb2N1bWVudChkb2N1bWVudElkKTtcbmV4cG9ydCBjb25zdCBkZWxldGVEb2N1bWVudCA9IChkb2N1bWVudElkOiBzdHJpbmcpID0+IGFwaUNsaWVudC5kZWxldGVEb2N1bWVudChkb2N1bWVudElkKTtcbmV4cG9ydCBjb25zdCBzZWFyY2hEb2N1bWVudHMgPSAocXVlcnk6IHN0cmluZywgbGltaXQ/OiBudW1iZXIpID0+IGFwaUNsaWVudC5zZWFyY2hEb2N1bWVudHMocXVlcnksIGxpbWl0KTtcbmV4cG9ydCBjb25zdCByZWluZGV4RG9jdW1lbnRzID0gKGRvY3VtZW50SWRzPzogc3RyaW5nW10pID0+IGFwaUNsaWVudC5yZWluZGV4RG9jdW1lbnRzKGRvY3VtZW50SWRzKTtcbmV4cG9ydCBjb25zdCBnZXREb2N1bWVudFN0YXRzID0gKCkgPT4gYXBpQ2xpZW50LmdldERvY3VtZW50U3RhdHMoKTtcbmV4cG9ydCBjb25zdCBnZXREb2N1bWVudENhdGVnb3JpZXMgPSAoKSA9PiBhcGlDbGllbnQuZ2V0RG9jdW1lbnRDYXRlZ29yaWVzKCk7XG5leHBvcnQgY29uc3QgZ2V0RG9jdW1lbnRUYWdzID0gKCkgPT4gYXBpQ2xpZW50LmdldERvY3VtZW50VGFncygpO1xuZXhwb3J0IGNvbnN0IGdldEhlYWx0aCA9ICgpID0+IGFwaUNsaWVudC5nZXRIZWFsdGgoKTtcbmV4cG9ydCBjb25zdCBnZXRBcGlJbmZvID0gKCkgPT4gYXBpQ2xpZW50LmdldEFwaUluZm8oKTtcbmV4cG9ydCBjb25zdCBnZXRTeXN0ZW1TdGF0cyA9ICgpID0+IGFwaUNsaWVudC5nZXRTeXN0ZW1TdGF0cygpO1xuIl0sIm5hbWVzIjpbImF4aW9zIiwiY29uZmlnIiwiQVBJX0VORFBPSU5UUyIsIkFwaUNsaWVudCIsImNvbnN0cnVjdG9yIiwiY2xpZW50IiwiY3JlYXRlIiwiYmFzZVVSTCIsImFwaVVybCIsInRpbWVvdXQiLCJoZWFkZXJzIiwiaW50ZXJjZXB0b3JzIiwicmVxdWVzdCIsInVzZSIsImVycm9yIiwiUHJvbWlzZSIsInJlamVjdCIsInJlc3BvbnNlIiwiYXBpRXJyb3IiLCJtZXNzYWdlIiwiZGF0YSIsImVycm9yRGF0YSIsImRldGFpbCIsImRldGFpbHMiLCJzZW5kTWVzc2FnZSIsInBvc3QiLCJjaGF0Iiwic2VuZE1lc3NhZ2VTdHJlYW0iLCJvbkNodW5rIiwib25Db21wbGV0ZSIsIm9uRXJyb3IiLCJmZXRjaCIsIm1ldGhvZCIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwib2siLCJFcnJvciIsInN0YXR1cyIsInJlYWRlciIsImdldFJlYWRlciIsImRlY29kZXIiLCJUZXh0RGVjb2RlciIsImZ1bGxSZXNwb25zZSIsImRvbmUiLCJ2YWx1ZSIsInJlYWQiLCJjaHVuayIsImRlY29kZSIsImxpbmVzIiwic3BsaXQiLCJsaW5lIiwic3RhcnRzV2l0aCIsInBhcnNlIiwic2xpY2UiLCJ0eXBlIiwiY29udGVudCIsImZ1bGxfY29udGVudCIsImZpbmFsX2NvbnRlbnQiLCJmYWxsYmFja19yZXNwb25zZSIsImUiLCJjb25zb2xlIiwid2FybiIsInN1Ym1pdEZlZWRiYWNrIiwiZmVlZGJhY2siLCJnZXRDaGF0SGlzdG9yeSIsInNlc3Npb25JZCIsImdldCIsImNsZWFyQ2hhdEhpc3RvcnkiLCJkZWxldGUiLCJnZXRDaGF0U3RhdHMiLCJ1cGxvYWREb2N1bWVudCIsImZpbGUiLCJjYXRlZ29yeSIsInRhZ3MiLCJkZXNjcmlwdGlvbiIsImZvcm1EYXRhIiwiRm9ybURhdGEiLCJhcHBlbmQiLCJsZW5ndGgiLCJqb2luIiwidXBsb2FkIiwiZ2V0RG9jdW1lbnRzIiwicGFnZSIsInBhZ2VTaXplIiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwidG9TdHJpbmciLCJwYWdlX3NpemUiLCJkb2N1bWVudHMiLCJnZXREb2N1bWVudCIsImRvY3VtZW50SWQiLCJkZWxldGVEb2N1bWVudCIsInNlYXJjaERvY3VtZW50cyIsInF1ZXJ5IiwibGltaXQiLCJyZWluZGV4RG9jdW1lbnRzIiwiZG9jdW1lbnRJZHMiLCJyZWluZGV4IiwiZG9jdW1lbnRfaWRzIiwiZ2V0RG9jdW1lbnRTdGF0cyIsImdldERvY3VtZW50Q2F0ZWdvcmllcyIsImdldERvY3VtZW50VGFncyIsImdldEhlYWx0aCIsImhlYWx0aCIsImdldEFwaUluZm8iLCJpbmZvIiwiZ2V0U3lzdGVtU3RhdHMiLCJzdGF0cyIsImFwaUNsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   COMPANY_INFO: () => (/* binding */ COMPANY_INFO),\n/* harmony export */   DEFAULT_MESSAGES: () => (/* binding */ DEFAULT_MESSAGES),\n/* harmony export */   STORAGE_KEYS: () => (/* binding */ STORAGE_KEYS),\n/* harmony export */   config: () => (/* binding */ config)\n/* harmony export */ });\n// Application configuration\nconst config = {\n    apiUrl: \"http://localhost:8000\" || 0,\n    appName: \"Flexible Soft Assistant\" || 0,\n    companyName: \"Flexible Soft\" || 0,\n    maxMessageLength: parseInt(\"2000\" || 0),\n    maxFileSize: parseInt(\"10485760\" || 0),\n    allowedFileTypes: (\"pdf,docx,txt,md\" || 0).split(\",\"),\n    supportedLanguages: (\"en,fr,ar\" || 0).split(\",\"),\n    defaultLanguage: \"en\" || 0,\n    enableFileUpload: \"true\" === \"true\",\n    enableFeedback: \"true\" === \"true\",\n    enableMultilingual: \"true\" === \"true\"\n};\nconst API_ENDPOINTS = {\n    chat: \"/api/chat\",\n    upload: \"/api/upload\",\n    documents: \"/api/documents\",\n    reindex: \"/api/reindex\",\n    feedback: \"/api/feedback\",\n    health: \"/health\",\n    info: \"/api/info\",\n    stats: \"/api/stats\"\n};\nconst STORAGE_KEYS = {\n    chatSessions: \"flexible-soft-chat-sessions\",\n    userPreferences: \"flexible-soft-user-preferences\",\n    currentSession: \"flexible-soft-current-session\"\n};\nconst DEFAULT_MESSAGES = {\n    en: {\n        welcome: `Hello! I'm the Flexible Soft AI Assistant. I can help you with:\n    \n• Questions about Flexible Soft services and policies\n• Technical guidance on software development\n• Information about our technologies and expertise\n• General programming and AI questions\n\nHow can I assist you today?`,\n        placeholder: \"Ask me anything about Flexible Soft or technical topics...\",\n        thinking: \"Thinking...\",\n        error: \"Sorry, I encountered an error. Please try again.\",\n        uploadSuccess: \"Document uploaded successfully!\",\n        uploadError: \"Failed to upload document. Please try again.\"\n    },\n    fr: {\n        welcome: `Bonjour ! Je suis l'assistant IA de Flexible Soft. Je peux vous aider avec :\n\n• Questions sur les services et politiques de Flexible Soft\n• Conseils techniques sur le développement logiciel\n• Informations sur nos technologies et expertise\n• Questions générales sur la programmation et l'IA\n\nComment puis-je vous aider aujourd'hui ?`,\n        placeholder: \"Posez-moi des questions sur Flexible Soft ou des sujets techniques...\",\n        thinking: \"R\\xe9flexion...\",\n        error: \"D\\xe9sol\\xe9, j'ai rencontr\\xe9 une erreur. Veuillez r\\xe9essayer.\",\n        uploadSuccess: \"Document t\\xe9l\\xe9charg\\xe9 avec succ\\xe8s !\",\n        uploadError: \"\\xc9chec du t\\xe9l\\xe9chargement du document. Veuillez r\\xe9essayer.\"\n    },\n    ar: {\n        welcome: `مرحباً! أنا مساعد الذكاء الاصطناعي لشركة Flexible Soft. يمكنني مساعدتك في:\n\n• أسئلة حول خدمات وسياسات Flexible Soft\n• إرشادات تقنية حول تطوير البرمجيات\n• معلومات حول تقنياتنا وخبراتنا\n• أسئلة عامة حول البرمجة والذكاء الاصطناعي\n\nكيف يمكنني مساعدتك اليوم؟`,\n        placeholder: \"اسألني أي شيء عن Flexible Soft أو المواضيع التقنية...\",\n        thinking: \"أفكر...\",\n        error: \"عذراً، واجهت خطأ. يرجى المحاولة مرة أخرى.\",\n        uploadSuccess: \"تم رفع المستند بنجاح!\",\n        uploadError: \"فشل في رفع المستند. يرجى المحاولة مرة أخرى.\"\n    }\n};\nconst COMPANY_INFO = {\n    name: \"Flexible Soft\",\n    shortName: \"Sofflex\",\n    domain: \"sofflex.com\",\n    description: \"A leading software, web, and AI development company\",\n    technologies: [\n        \"React\",\n        \"Next.js\",\n        \"TypeScript\",\n        \"Python\",\n        \"AI/ML\"\n    ],\n    services: [\n        \"Software Development\",\n        \"Web Development\",\n        \"AI Solutions\",\n        \"Mobile Applications\",\n        \"Cloud Services\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   exportChatHistory: () => (/* binding */ exportChatHistory),\n/* harmony export */   extractCodeBlocks: () => (/* binding */ extractCodeBlocks),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   formatTimestamp: () => (/* binding */ formatTimestamp),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateSessionTitle: () => (/* binding */ generateSessionTitle),\n/* harmony export */   getFileIcon: () => (/* binding */ getFileIcon),\n/* harmony export */   getLanguageDirection: () => (/* binding */ getLanguageDirection),\n/* harmony export */   getLanguageName: () => (/* binding */ getLanguageName),\n/* harmony export */   highlightSearchTerms: () => (/* binding */ highlightSearchTerms),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl),\n/* harmony export */   sanitizeHtml: () => (/* binding */ sanitizeHtml),\n/* harmony export */   scrollToBottom: () => (/* binding */ scrollToBottom),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateFileSize: () => (/* binding */ validateFileSize),\n/* harmony export */   validateFileType: () => (/* binding */ validateFileType)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isToday,isYesterday!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isToday,isYesterday!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isToday,isYesterday!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/isYesterday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,formatDistanceToNow,isToday,isYesterday!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/formatDistanceToNow/index.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm-node/v4.js\");\n// Utility functions\n\n\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction generateId() {\n    return (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n}\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\nfunction formatTimestamp(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    if ((0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(dateObj)) {\n        return (0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(dateObj, \"HH:mm\");\n    } else if ((0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(dateObj)) {\n        return \"Yesterday \" + (0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(dateObj, \"HH:mm\");\n    } else {\n        return (0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(dateObj, \"MMM dd, HH:mm\");\n    }\n}\nfunction formatRelativeTime(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return (0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(dateObj, {\n        addSuffix: true\n    });\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\nfunction validateFileType(file, allowedTypes) {\n    const fileExtension = file.name.split(\".\").pop()?.toLowerCase();\n    return fileExtension ? allowedTypes.includes(fileExtension) : false;\n}\nfunction validateFileSize(file, maxSize) {\n    return file.size <= maxSize;\n}\nfunction getFileIcon(fileType) {\n    const icons = {\n        pdf: \"\\uD83D\\uDCC4\",\n        docx: \"\\uD83D\\uDCDD\",\n        txt: \"\\uD83D\\uDCC4\",\n        md: \"\\uD83D\\uDCDD\"\n    };\n    return icons[fileType.toLowerCase()] || \"\\uD83D\\uDCC4\";\n}\nfunction copyToClipboard(text) {\n    if (navigator.clipboard && window.isSecureContext) {\n        return navigator.clipboard.writeText(text);\n    } else {\n        // Fallback for older browsers\n        const textArea = document.createElement(\"textarea\");\n        textArea.value = text;\n        textArea.style.position = \"fixed\";\n        textArea.style.left = \"-999999px\";\n        textArea.style.top = \"-999999px\";\n        document.body.appendChild(textArea);\n        textArea.focus();\n        textArea.select();\n        return new Promise((resolve, reject)=>{\n            if (document.execCommand(\"copy\")) {\n                resolve();\n            } else {\n                reject(new Error(\"Copy failed\"));\n            }\n            document.body.removeChild(textArea);\n        });\n    }\n}\nfunction scrollToBottom(element, smooth = true) {\n    element.scrollTo({\n        top: element.scrollHeight,\n        behavior: smooth ? \"smooth\" : \"auto\"\n    });\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\nfunction getLanguageDirection(language) {\n    return language === \"ar\" ? \"rtl\" : \"ltr\";\n}\nfunction getLanguageName(language) {\n    const names = {\n        en: \"English\",\n        fr: \"Fran\\xe7ais\",\n        ar: \"العربية\"\n    };\n    return names[language] || language;\n}\nfunction extractCodeBlocks(text) {\n    const codeBlockRegex = /```(\\w+)?\\n([\\s\\S]*?)```/g;\n    const blocks = [];\n    let match;\n    while((match = codeBlockRegex.exec(text)) !== null){\n        blocks.push({\n            language: match[1] || \"text\",\n            code: match[2].trim()\n        });\n    }\n    return blocks;\n}\nfunction highlightSearchTerms(text, searchTerm) {\n    if (!searchTerm.trim()) return text;\n    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\")})`, \"gi\");\n    return text.replace(regex, \"<mark>$1</mark>\");\n}\nfunction generateSessionTitle(messages) {\n    const firstUserMessage = messages.find((m)=>m.role === \"user\");\n    if (!firstUserMessage) return \"New Chat\";\n    const title = truncateText(firstUserMessage.content, 50);\n    return title || \"New Chat\";\n}\nfunction exportChatHistory(messages, sessionId) {\n    const content = messages.map((msg)=>`[${formatTimestamp(msg.timestamp)}] ${msg.role.toUpperCase()}: ${msg.content}`).join(\"\\n\\n\");\n    const blob = new Blob([\n        content\n    ], {\n        type: \"text/plain\"\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.href = url;\n    a.download = `chat-history-${sessionId}-${(0,_barrel_optimize_names_format_formatDistanceToNow_isToday_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(new Date(), \"yyyy-MM-dd\")}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n}\nfunction isValidUrl(string) {\n    try {\n        new URL(string);\n        return true;\n    } catch (_) {\n        return false;\n    }\n}\nfunction sanitizeHtml(html) {\n    const div = document.createElement(\"div\");\n    div.textContent = html;\n    return div.innerHTML;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6bba6213920c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmxleGlibGUtc29mdC1jaGF0Ym90LWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz83MzMyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNmJiYTYyMTM5MjBjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./src/lib/config.ts\");\n\n\n\n\n\nconst metadata = {\n    title: _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.appName,\n    description: `AI-powered knowledge assistant for ${_lib_config__WEBPACK_IMPORTED_MODULE_3__.config.companyName}`,\n    keywords: [\n        \"AI\",\n        \"chatbot\",\n        \"assistant\",\n        \"Flexible Soft\",\n        \"Sofflex\"\n    ],\n    authors: [\n        {\n            name: _lib_config__WEBPACK_IMPORTED_MODULE_3__.config.companyName\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"noindex, nofollow\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 4000,\n                        style: {\n                            background: \"#363636\",\n                            color: \"#fff\"\n                        },\n                        success: {\n                            duration: 3000,\n                            iconTheme: {\n                                primary: \"#10b981\",\n                                secondary: \"#fff\"\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            iconTheme: {\n                                primary: \"#ef4444\",\n                                secondary: \"#fff\"\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Chatyy\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\Chatyy\frontend\src\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   COMPANY_INFO: () => (/* binding */ COMPANY_INFO),\n/* harmony export */   DEFAULT_MESSAGES: () => (/* binding */ DEFAULT_MESSAGES),\n/* harmony export */   STORAGE_KEYS: () => (/* binding */ STORAGE_KEYS),\n/* harmony export */   config: () => (/* binding */ config)\n/* harmony export */ });\n// Application configuration\nconst config = {\n    apiUrl: \"http://localhost:8000\" || 0,\n    appName: \"Flexible Soft Assistant\" || 0,\n    companyName: \"Flexible Soft\" || 0,\n    maxMessageLength: parseInt(\"2000\" || 0),\n    maxFileSize: parseInt(\"10485760\" || 0),\n    allowedFileTypes: (\"pdf,docx,txt,md\" || 0).split(\",\"),\n    supportedLanguages: (\"en,fr,ar\" || 0).split(\",\"),\n    defaultLanguage: \"en\" || 0,\n    enableFileUpload: \"true\" === \"true\",\n    enableFeedback: \"true\" === \"true\",\n    enableMultilingual: \"true\" === \"true\"\n};\nconst API_ENDPOINTS = {\n    chat: \"/api/chat\",\n    upload: \"/api/upload\",\n    documents: \"/api/documents\",\n    reindex: \"/api/reindex\",\n    feedback: \"/api/feedback\",\n    health: \"/health\",\n    info: \"/api/info\",\n    stats: \"/api/stats\"\n};\nconst STORAGE_KEYS = {\n    chatSessions: \"flexible-soft-chat-sessions\",\n    userPreferences: \"flexible-soft-user-preferences\",\n    currentSession: \"flexible-soft-current-session\"\n};\nconst DEFAULT_MESSAGES = {\n    en: {\n        welcome: `Hello! I'm the Flexible Soft AI Assistant. I can help you with:\n    \n• Questions about Flexible Soft services and policies\n• Technical guidance on software development\n• Information about our technologies and expertise\n• General programming and AI questions\n\nHow can I assist you today?`,\n        placeholder: \"Ask me anything about Flexible Soft or technical topics...\",\n        thinking: \"Thinking...\",\n        error: \"Sorry, I encountered an error. Please try again.\",\n        uploadSuccess: \"Document uploaded successfully!\",\n        uploadError: \"Failed to upload document. Please try again.\"\n    },\n    fr: {\n        welcome: `Bonjour ! Je suis l'assistant IA de Flexible Soft. Je peux vous aider avec :\n\n• Questions sur les services et politiques de Flexible Soft\n• Conseils techniques sur le développement logiciel\n• Informations sur nos technologies et expertise\n• Questions générales sur la programmation et l'IA\n\nComment puis-je vous aider aujourd'hui ?`,\n        placeholder: \"Posez-moi des questions sur Flexible Soft ou des sujets techniques...\",\n        thinking: \"R\\xe9flexion...\",\n        error: \"D\\xe9sol\\xe9, j'ai rencontr\\xe9 une erreur. Veuillez r\\xe9essayer.\",\n        uploadSuccess: \"Document t\\xe9l\\xe9charg\\xe9 avec succ\\xe8s !\",\n        uploadError: \"\\xc9chec du t\\xe9l\\xe9chargement du document. Veuillez r\\xe9essayer.\"\n    },\n    ar: {\n        welcome: `مرحباً! أنا مساعد الذكاء الاصطناعي لشركة Flexible Soft. يمكنني مساعدتك في:\n\n• أسئلة حول خدمات وسياسات Flexible Soft\n• إرشادات تقنية حول تطوير البرمجيات\n• معلومات حول تقنياتنا وخبراتنا\n• أسئلة عامة حول البرمجة والذكاء الاصطناعي\n\nكيف يمكنني مساعدتك اليوم؟`,\n        placeholder: \"اسألني أي شيء عن Flexible Soft أو المواضيع التقنية...\",\n        thinking: \"أفكر...\",\n        error: \"عذراً، واجهت خطأ. يرجى المحاولة مرة أخرى.\",\n        uploadSuccess: \"تم رفع المستند بنجاح!\",\n        uploadError: \"فشل في رفع المستند. يرجى المحاولة مرة أخرى.\"\n    }\n};\nconst COMPANY_INFO = {\n    name: \"Flexible Soft\",\n    shortName: \"Sofflex\",\n    domain: \"sofflex.com\",\n    description: \"A leading software, web, and AI development company\",\n    technologies: [\n        \"React\",\n        \"Next.js\",\n        \"TypeScript\",\n        \"Python\",\n        \"AI/ML\"\n    ],\n    services: [\n        \"Software Development\",\n        \"Web Development\",\n        \"AI Solutions\",\n        \"Mobile Applications\",\n        \"Cloud Services\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/refractor","vendor-chunks/next","vendor-chunks/axios","vendor-chunks/date-fns","vendor-chunks/mdast-util-to-markdown","vendor-chunks/mdast-util-to-hast","vendor-chunks/lucide-react","vendor-chunks/micromark-core-commonmark","vendor-chunks/hastscript","vendor-chunks/property-information","vendor-chunks/@babel","vendor-chunks/asynckit","vendor-chunks/micromark","vendor-chunks/math-intrinsics","vendor-chunks/react-syntax-highlighter","vendor-chunks/es-errors","vendor-chunks/@swc","vendor-chunks/uuid","vendor-chunks/call-bind-apply-helpers","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/react-hot-toast","vendor-chunks/micromark-util-subtokenize","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/style-to-js","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/tailwind-merge","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-gfm","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-gfm","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/goober","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/clsx","vendor-chunks/character-entities","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/xtend","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/inline-style-parser","vendor-chunks/hast-util-parse-selector","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/extend","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CPC4%5CDocuments%5Caugment-projects%5CChatyy%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC4%5CDocuments%5Caugment-projects%5CChatyy%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
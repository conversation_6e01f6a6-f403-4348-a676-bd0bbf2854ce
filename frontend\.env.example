# FS - Flexible Soft AI Assistant Frontend Configuration
# Enhanced environment configuration for Next.js frontend

# =============================================================================
# API Configuration
# =============================================================================
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_TIMEOUT=30000

# =============================================================================
# App Configuration
# =============================================================================
NEXT_PUBLIC_APP_NAME=FS - Flexible Soft AI Assistant
NEXT_PUBLIC_APP_DESCRIPTION=Enhanced AI-powered assistant with dynamic reasoning
NEXT_PUBLIC_COMPANY_NAME=Flexible Soft
NEXT_PUBLIC_COMPANY_SHORT_NAME=Sofflex
NEXT_PUBLIC_COMPANY_DOMAIN=sofflex.com
NEXT_PUBLIC_COMPANY_EMAIL=<EMAIL>
NEXT_PUBLIC_COMPANY_WEBSITE=https://sofflex-website.dev.flexible-soft.com/

# =============================================================================
# Feature Flags
# =============================================================================
NEXT_PUBLIC_ENABLE_FILE_UPLOAD=false
NEXT_PUBLIC_ENABLE_FEEDBACK=true
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_MULTILINGUAL=true
NEXT_PUBLIC_ENABLE_STREAMING=true
NEXT_PUBLIC_ENABLE_VOICE_INPUT=false
NEXT_PUBLIC_ENABLE_DARK_MODE=true

# =============================================================================
# Language Configuration
# =============================================================================
NEXT_PUBLIC_DEFAULT_LANGUAGE=en
NEXT_PUBLIC_SUPPORTED_LANGUAGES=en,ar
NEXT_PUBLIC_ENABLE_AUTO_LANGUAGE_DETECTION=true

# =============================================================================
# UI Configuration
# =============================================================================
NEXT_PUBLIC_THEME=light
NEXT_PUBLIC_MAX_MESSAGE_LENGTH=2000
NEXT_PUBLIC_SHOW_TIMESTAMPS=true
NEXT_PUBLIC_SHOW_MESSAGE_ACTIONS=true
NEXT_PUBLIC_ENABLE_MESSAGE_COPY=true
NEXT_PUBLIC_ENABLE_MESSAGE_RETRY=true

# =============================================================================
# Chat Configuration
# =============================================================================
NEXT_PUBLIC_MAX_CHAT_HISTORY=50
NEXT_PUBLIC_TYPING_DELAY=1000
NEXT_PUBLIC_AUTO_SCROLL=true
NEXT_PUBLIC_STREAMING_DELAY=50
NEXT_PUBLIC_MAX_SESSIONS=20

# =============================================================================
# Performance Configuration
# =============================================================================
NEXT_PUBLIC_ENABLE_LAZY_LOADING=true
NEXT_PUBLIC_ENABLE_VIRTUAL_SCROLLING=false
NEXT_PUBLIC_DEBOUNCE_DELAY=300

# =============================================================================
# Development Settings
# =============================================================================
NEXT_PUBLIC_DEBUG=false
NEXT_PUBLIC_SHOW_DEV_TOOLS=false
NEXT_PUBLIC_ENABLE_CONSOLE_LOGS=false

# =============================================================================
# Legacy Configuration (for compatibility)
# =============================================================================
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=pdf,docx,txt,md

// API client for Flexible Soft Chatbot
import axios, { AxiosInstance, AxiosError } from 'axios';
import { config, API_ENDPOINTS } from './config';
import {
  ChatRequest,
  ChatResponse,
  Document,
  DocumentUploadResponse,
  FeedbackRequest,
  SystemStats,
  ChatStats,
  ApiError,
} from '@/types';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.apiUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add any auth headers here if needed
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        const apiError: ApiError = {
          error: 'API Error',
          message: 'An unexpected error occurred',
        };

        if (error.response?.data) {
          const errorData = error.response.data as any;
          apiError.error = errorData.error || 'API Error';
          apiError.message = errorData.message || errorData.detail || 'An unexpected error occurred';
          apiError.details = errorData;
        } else if (error.request) {
          apiError.error = 'Network Error';
          apiError.message = 'Unable to connect to the server. Please check your internet connection.';
        } else {
          apiError.message = error.message || 'An unexpected error occurred';
        }

        return Promise.reject(apiError);
      }
    );
  }

  // Chat endpoints
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    const response = await this.client.post<ChatResponse>(API_ENDPOINTS.chat, request);
    return response.data;
  }

  // Streaming chat endpoint
  async sendMessageStream(
    request: ChatRequest,
    onChunk: (chunk: string) => void,
    onComplete: (fullResponse: string) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      const response = await fetch(`${config.apiUrl}${API_ENDPOINTS.chat}/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let fullResponse = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));

              if (data.type === 'chunk') {
                onChunk(data.content);
                fullResponse = data.full_content;
              } else if (data.type === 'end') {
                onComplete(data.final_content || fullResponse);
                return;
              } else if (data.type === 'error') {
                onError(data.error);
                if (data.fallback_response) {
                  onComplete(data.fallback_response);
                }
                return;
              }
            } catch (e) {
              console.warn('Failed to parse streaming data:', line);
            }
          }
        }
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Streaming failed');
    }
  }

  async submitFeedback(request: FeedbackRequest): Promise<{ success: boolean; message: string }> {
    const response = await this.client.post(API_ENDPOINTS.feedback, request);
    return response.data;
  }

  async getChatHistory(sessionId: string): Promise<{ session_id: string; messages: any[] }> {
    const response = await this.client.get(`${API_ENDPOINTS.chat}/history/${sessionId}`);
    return response.data;
  }

  async clearChatHistory(sessionId: string): Promise<{ message: string }> {
    const response = await this.client.delete(`${API_ENDPOINTS.chat}/history/${sessionId}`);
    return response.data;
  }

  async getChatStats(): Promise<ChatStats> {
    const response = await this.client.get<ChatStats>(`${API_ENDPOINTS.chat}/stats`);
    return response.data;
  }

  // Document endpoints
  async uploadDocument(
    file: File,
    category?: string,
    tags?: string[],
    description?: string
  ): Promise<DocumentUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (category) formData.append('category', category);
    if (tags && tags.length > 0) formData.append('tags', tags.join(','));
    if (description) formData.append('description', description);

    const response = await this.client.post<DocumentUploadResponse>(
      API_ENDPOINTS.upload,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  }

  async getDocuments(
    page = 1,
    pageSize = 50,
    category?: string,
    status?: string
  ): Promise<{
    documents: Document[];
    total_count: number;
    page: number;
    page_size: number;
  }> {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
    });

    if (category) params.append('category', category);
    if (status) params.append('status', status);

    const response = await this.client.get(`${API_ENDPOINTS.documents}?${params}`);
    return response.data;
  }

  async getDocument(documentId: string): Promise<Document> {
    const response = await this.client.get<Document>(`${API_ENDPOINTS.documents}/${documentId}`);
    return response.data;
  }

  async deleteDocument(documentId: string): Promise<{ message: string }> {
    const response = await this.client.delete(`${API_ENDPOINTS.documents}/${documentId}`);
    return response.data;
  }

  async searchDocuments(query: string, limit = 10): Promise<{
    results: any[];
    total_results: number;
    query: string;
    processing_time: number;
  }> {
    const response = await this.client.post(`${API_ENDPOINTS.documents}/search`, {
      query,
      limit,
    });
    return response.data;
  }

  async reindexDocuments(documentIds?: string[]): Promise<{
    success: boolean;
    message: string;
    processed_documents: number;
    failed_documents: number;
    processing_time: number;
  }> {
    const response = await this.client.post(API_ENDPOINTS.reindex, {
      document_ids: documentIds,
    });
    return response.data;
  }

  async getDocumentStats(): Promise<any> {
    const response = await this.client.get(`${API_ENDPOINTS.documents}/stats`);
    return response.data;
  }

  async getDocumentCategories(): Promise<{ categories: string[] }> {
    const response = await this.client.get(`${API_ENDPOINTS.documents}/categories`);
    return response.data;
  }

  async getDocumentTags(): Promise<{ tags: string[] }> {
    const response = await this.client.get(`${API_ENDPOINTS.documents}/tags`);
    return response.data;
  }

  // System endpoints
  async getHealth(): Promise<{
    status: string;
    services: Record<string, boolean>;
    timestamp: string;
  }> {
    const response = await this.client.get(API_ENDPOINTS.health);
    return response.data;
  }

  async getApiInfo(): Promise<{
    company: any;
    features: any;
    limits: any;
    model_info: any;
  }> {
    const response = await this.client.get(API_ENDPOINTS.info);
    return response.data;
  }

  async getSystemStats(): Promise<SystemStats> {
    const response = await this.client.get<SystemStats>(API_ENDPOINTS.stats);
    return response.data;
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export individual methods for convenience (properly bound)
export const sendMessage = (request: ChatRequest) => apiClient.sendMessage(request);
export const sendMessageStream = (
  request: ChatRequest,
  onChunk: (chunk: string) => void,
  onComplete: (fullResponse: string) => void,
  onError: (error: string) => void
) => apiClient.sendMessageStream(request, onChunk, onComplete, onError);
export const submitFeedback = (request: FeedbackRequest) => apiClient.submitFeedback(request);
export const getChatHistory = (sessionId: string) => apiClient.getChatHistory(sessionId);
export const clearChatHistory = (sessionId: string) => apiClient.clearChatHistory(sessionId);
export const getChatStats = () => apiClient.getChatStats();
export const uploadDocument = (file: File, category?: string, tags?: string[], description?: string) =>
  apiClient.uploadDocument(file, category, tags, description);
export const getDocuments = (page?: number, pageSize?: number, category?: string, status?: string) =>
  apiClient.getDocuments(page, pageSize, category, status);
export const getDocument = (documentId: string) => apiClient.getDocument(documentId);
export const deleteDocument = (documentId: string) => apiClient.deleteDocument(documentId);
export const searchDocuments = (query: string, limit?: number) => apiClient.searchDocuments(query, limit);
export const reindexDocuments = (documentIds?: string[]) => apiClient.reindexDocuments(documentIds);
export const getDocumentStats = () => apiClient.getDocumentStats();
export const getDocumentCategories = () => apiClient.getDocumentCategories();
export const getDocumentTags = () => apiClient.getDocumentTags();
export const getHealth = () => apiClient.getHealth();
export const getApiInfo = () => apiClient.getApiInfo();
export const getSystemStats = () => apiClient.getSystemStats();

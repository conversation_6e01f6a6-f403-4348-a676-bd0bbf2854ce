'use client';

import { Language } from '@/types';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Copy, Check } from 'lucide-react';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface MessageContentProps {
  content: string;
  language: Language;
}

export function MessageContent({ content, language }: MessageContentProps) {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  return (
    <div className={cn(
      'prose prose-sm max-w-none',
      language === 'ar' && 'prose-rtl'
    )}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          // Custom code block component
          code({ node, className, children, ...props }: any) {
            const inline = !className;
            const match = /language-(\w+)/.exec(className || '');
            const codeContent = String(children).replace(/\n$/, '');
            
            if (!inline && match) {
              return (
                <div className="relative group">
                  <div className="flex items-center justify-between bg-slate-800 text-slate-200 px-4 py-2 text-sm rounded-t-lg">
                    <span className="font-medium">{match[1]}</span>
                    <button
                      onClick={() => handleCopyCode(codeContent)}
                      className="flex items-center gap-1 px-2 py-1 text-xs bg-slate-700 hover:bg-slate-600 rounded transition-colors duration-200"
                    >
                      {copiedCode === codeContent ? (
                        <>
                          <Check className="w-3 h-3" />
                          Copied
                        </>
                      ) : (
                        <>
                          <Copy className="w-3 h-3" />
                          Copy
                        </>
                      )}
                    </button>
                  </div>
                  <SyntaxHighlighter
                    style={oneDark}
                    language={match[1]}
                    PreTag="div"
                    className="!mt-0 !rounded-t-none"
                    {...props}
                  >
                    {codeContent}
                  </SyntaxHighlighter>
                </div>
              );
            }

            return (
              <code className="bg-slate-100 text-slate-900 px-1 py-0.5 rounded text-sm" {...props}>
                {children}
              </code>
            );
          },
          
          // Custom link component
          a({ href, children, ...props }) {
            return (
              <a
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary-600 hover:text-primary-700 underline"
                {...props}
              >
                {children}
              </a>
            );
          },
          
          // Custom list components
          ul({ children, ...props }) {
            return (
              <ul className="list-disc list-inside space-y-1" {...props}>
                {children}
              </ul>
            );
          },
          
          ol({ children, ...props }) {
            return (
              <ol className="list-decimal list-inside space-y-1" {...props}>
                {children}
              </ol>
            );
          },
          
          // Custom blockquote component
          blockquote({ children, ...props }) {
            return (
              <blockquote 
                className="border-l-4 border-primary-200 pl-4 py-2 bg-primary-50 rounded-r-lg italic"
                {...props}
              >
                {children}
              </blockquote>
            );
          },
          
          // Custom table components
          table({ children, ...props }) {
            return (
              <div className="overflow-x-auto">
                <table className="min-w-full border border-slate-200 rounded-lg" {...props}>
                  {children}
                </table>
              </div>
            );
          },
          
          th({ children, ...props }) {
            return (
              <th 
                className="px-4 py-2 bg-slate-50 border-b border-slate-200 text-left font-medium text-slate-900"
                {...props}
              >
                {children}
              </th>
            );
          },
          
          td({ children, ...props }) {
            return (
              <td className="px-4 py-2 border-b border-slate-200" {...props}>
                {children}
              </td>
            );
          },
          
          // Custom heading components
          h1({ children, ...props }) {
            return (
              <h1 className="text-xl font-bold text-slate-900 mb-3" {...props}>
                {children}
              </h1>
            );
          },
          
          h2({ children, ...props }) {
            return (
              <h2 className="text-lg font-semibold text-slate-900 mb-2" {...props}>
                {children}
              </h2>
            );
          },
          
          h3({ children, ...props }) {
            return (
              <h3 className="text-base font-medium text-slate-900 mb-2" {...props}>
                {children}
              </h3>
            );
          },
          
          // Custom paragraph component
          p({ children, ...props }) {
            return (
              <p className="mb-3 leading-relaxed" {...props}>
                {children}
              </p>
            );
          },
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}

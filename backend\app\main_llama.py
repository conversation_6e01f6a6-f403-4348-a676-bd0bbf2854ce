"""
FS - Flexible Soft AI Assistant
Real LLaMA-powered intelligent conversation system
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List, Dict
from datetime import datetime
import uuid
import uvicorn
import torch
import logging
import asyncio
from threading import Lock

# Import settings
from .config_simple import settings

app = FastAPI(
    title="FS - Flexible Soft AI Assistant (LLaMA-Powered)",
    description="Truly intelligent AI assistant powered by LLaMA models",
    version="5.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    language: Optional[str] = "en"

class ChatResponse(BaseModel):
    message: str
    session_id: str
    timestamp: datetime
    sources: Optional[List[str]] = None

# Global variables for model
model = None
tokenizer = None
model_lock = Lock()
current_model_name = None
conversation_history: Dict[str, List[Dict]] = {}

# System prompt for truly intelligent conversation
SYSTEM_PROMPT = """You are FS, the Flexible Soft AI Assistant.
You work for Flexible Soft (Sofflex) — an IT company specialized in web development, mobile apps, AI solutions, e-commerce, and tech consulting.

Your mission:
- Answer ANY user question — not just about the company.
- If a question is general (like cooking or science), answer normally and politely, and optionally mention how Flexible Soft's expertise connects to it.
- Speak in a friendly, professional tone — like a smart human consultant.
- Correct grammar or spelling automatically without pointing it out.
- If the question is about Flexible Soft, give specific, accurate info from the company context.
- Never repeat the user's input or say "interesting question".
- Always adapt to the user's tone (casual, formal, Arabic, English, mixed).
- Be helpful, curious, and intelligent.

Company Context:
- Flexible Soft (Sofflex): IT company in Saudi Arabia & Middle East
- Services: Next.js/React websites, Flutter mobile apps, Python/FastAPI backends, AI chatbots, e-commerce platforms, business automation
- Tech Stack: Next.js, React, TypeScript, Python, FastAPI, Flutter, AI/ML, databases
- Contact: <EMAIL> | Website: https://sofflex-website.dev.flexible-soft.com/
- Specializes in custom solutions for Saudi market with Arabic language support

Response Guidelines:
- Be conversational and natural
- Use appropriate emojis sparingly
- Provide practical, actionable advice
- Connect topics to technology solutions when relevant
- Support both Arabic and English seamlessly"""

def load_llama_model():
    """Load LLaMA model and tokenizer"""
    global model, tokenizer, current_model_name
    
    try:
        from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
        import torch
        
        # Model selection (try open models first, then LLaMA if available)
        model_options = [
            "microsoft/DialoGPT-medium",  # Smaller, faster model
            "microsoft/DialoGPT-large",
            "microsoft/DialoGPT-small",
            "meta-llama/Llama-3-8b-chat-hf",
            "meta-llama/Llama-2-13b-chat-hf",
            "meta-llama/Llama-2-7b-chat-hf"
        ]
        
        model_name = None
        for option in model_options:
            try:
                print(f"Attempting to load {option}...")
                
                # Configure quantization for memory efficiency
                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True,
                    bnb_4bit_quant_type="nf4"
                )
                
                # Load tokenizer
                tokenizer = AutoTokenizer.from_pretrained(option)
                if tokenizer.pad_token is None:
                    tokenizer.pad_token = tokenizer.eos_token
                
                # Load model with quantization
                model = AutoModelForCausalLM.from_pretrained(
                    option,
                    quantization_config=quantization_config,
                    device_map="auto",
                    torch_dtype=torch.float16,
                    trust_remote_code=True
                )
                
                current_model_name = option
                print(f"✅ Successfully loaded {current_model_name}")
                break
                
            except Exception as e:
                print(f"❌ Failed to load {option}: {e}")
                continue
        
        if model is None:
            raise Exception("Failed to load any LLaMA model")
            
        return True
        
    except Exception as e:
        print(f"❌ Error loading LLaMA model: {e}")
        print("💡 Make sure you have:")
        print("   - pip install transformers torch accelerate bitsandbytes")
        print("   - Hugging Face access to LLaMA models")
        print("   - Sufficient GPU memory or use CPU fallback")
        return False

def format_conversation_for_model(messages: List[Dict], user_message: str, model_name: str) -> str:
    """Format conversation history for different model types"""

    if "llama" in model_name.lower():
        # LLaMA format
        conversation = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n{SYSTEM_PROMPT}<|eot_id|>\n"

        # Add conversation history (keep last 6 messages to stay within token limits)
        recent_messages = messages[-6:] if len(messages) > 6 else messages

        for msg in recent_messages:
            role = msg["role"]
            content = msg["content"]
            conversation += f"<|start_header_id|>{role}<|end_header_id|>\n{content}<|eot_id|>\n"

        # Add current user message
        conversation += f"<|start_header_id|>user<|end_header_id|>\n{user_message}<|eot_id|>\n"
        conversation += "<|start_header_id|>assistant<|end_header_id|>\n"

    elif "dialogpt" in model_name.lower():
        # DialoGPT format - optimized for conversational AI
        conversation = ""

        # Add conversation history (DialoGPT works better with just the conversation)
        recent_messages = messages[-4:] if len(messages) > 4 else messages

        for msg in recent_messages:
            conversation += f"{msg['content']}<|endoftext|>"

        # Add current user message
        conversation += f"{user_message}<|endoftext|>"

    else:
        # Generic format
        conversation = f"System: {SYSTEM_PROMPT}\n\nUser: {user_message}\nAssistant:"

    return conversation

async def get_llama_response(user_message: str, session_id: str) -> str:
    """Get intelligent response from AI model with clean conversation format"""
    global model, tokenizer, conversation_history

    if model is None or tokenizer is None:
        return get_simple_intelligent_response(user_message)

    try:
        # Get or initialize conversation history
        if session_id not in conversation_history:
            conversation_history[session_id] = []

        # Create clean message format for AI model
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT}
        ]

        # Add recent conversation history (last 6 messages to stay within token limits)
        recent_messages = conversation_history[session_id][-6:] if len(conversation_history[session_id]) > 6 else conversation_history[session_id]
        messages.extend(recent_messages)

        # Add current user message
        messages.append({"role": "user", "content": user_message})

        # Format for the specific model
        if current_model_name and "dialogpt" in current_model_name.lower():
            # DialoGPT format - include system context in conversation
            conversation_text = f"FS Assistant: I'm FS from Flexible Soft, an IT company. I help with any questions about technology, business, or general topics.<|endoftext|>"

            # Add recent conversation history
            for msg in messages[-3:]:  # Last 3 messages for context
                if msg["role"] == "user":
                    conversation_text += f"User: {msg['content']}<|endoftext|>"
                elif msg["role"] == "assistant":
                    conversation_text += f"FS Assistant: {msg['content']}<|endoftext|>"

            # Add current user message
            conversation_text += f"User: {user_message}<|endoftext|>FS Assistant:"

            # Tokenize and generate
            inputs = tokenizer.encode(conversation_text, return_tensors="pt")
            if torch.cuda.is_available():
                inputs = inputs.to("cuda")

            with model_lock:
                with torch.no_grad():
                    outputs = model.generate(
                        inputs,
                        max_new_tokens=150,
                        temperature=0.7,
                        do_sample=True,
                        top_p=0.9,
                        repetition_penalty=1.1,
                        pad_token_id=tokenizer.eos_token_id,
                        eos_token_id=tokenizer.eos_token_id
                    )

            # Decode response
            response = tokenizer.decode(outputs[0][inputs.shape[-1]:], skip_special_tokens=True)
            response = response.replace("<|endoftext|>", "").strip()

        else:
            # LLaMA format
            formatted_conversation = format_conversation_for_model(
                conversation_history[session_id],
                user_message,
                current_model_name or "generic"
            )

            inputs = tokenizer(
                formatted_conversation,
                return_tensors="pt",
                truncation=True,
                max_length=4000,
                padding=True
            )

            if torch.cuda.is_available():
                inputs = {k: v.to("cuda") for k, v in inputs.items()}

            with model_lock:
                with torch.no_grad():
                    outputs = model.generate(
                        **inputs,
                        max_new_tokens=300,
                        temperature=0.7,
                        do_sample=True,
                        top_p=0.9,
                        repetition_penalty=1.1,
                        pad_token_id=tokenizer.eos_token_id,
                        eos_token_id=tokenizer.eos_token_id
                    )

            full_response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = full_response.split("Assistant:")[-1].strip()

        # Clean and validate response
        if response and len(response) > 3 and response != user_message and not response.startswith("You are FS"):
            # Update conversation history
            conversation_history[session_id].append({"role": "user", "content": user_message})
            conversation_history[session_id].append({"role": "assistant", "content": response})

            # Limit conversation history
            if len(conversation_history[session_id]) > 20:
                conversation_history[session_id] = conversation_history[session_id][-20:]

            return response
        else:
            # Fallback to intelligent response if AI output is poor
            print(f"⚠️ AI response validation failed: '{response}' - using fallback")
            return get_simple_intelligent_response(user_message)

    except Exception as e:
        print(f"❌ Error generating AI response: {e}")
        return get_simple_intelligent_response(user_message)

def get_simple_intelligent_response(user_message: str) -> str:
    """Simple but intelligent response when AI model is unavailable"""
    msg = user_message.lower().strip()

    # Detect Arabic
    is_arabic = any(char in user_message for char in ["ا", "ب", "ت", "ث", "ج", "ح", "خ", "د", "ذ", "ر", "ز", "س", "ش", "ص", "ض", "ط", "ظ", "ع", "غ", "ف", "ق", "ك", "ل", "م", "ن", "ه", "و", "ي"])

    # Company questions
    if any(word in msg for word in ["sofflex", "flexible soft", "your company", "services", "what u do", "what you do", "what do you do", "about you", "who are you"]):
        if is_arabic:
            return """🏢 **Flexible Soft (سوفليكس)**

نحن شركة تطوير برمجيات متخصصة في:
• تطوير المواقع (Next.js, React)
• تطبيقات الجوال (Flutter)
• حلول الذكاء الاصطناعي
• التجارة الإلكترونية والأتمتة

نخدم السوق السعودي والشرق الأوسط بحلول تقنية مخصصة.

📧 <EMAIL>
🌐 https://sofflex-website.dev.flexible-soft.com/"""
        else:
            return """🏢 **Flexible Soft (Sofflex)**

We're an IT company specializing in:
• Web development (Next.js, React)
• Mobile apps (Flutter)
• AI solutions & automation
• E-commerce platforms

We serve Saudi Arabia & Middle East with custom tech solutions.

📧 <EMAIL>
🌐 https://sofflex-website.dev.flexible-soft.com/"""

    # AI/Tech questions
    elif any(word in msg for word in ["ai", "artificial intelligence", "python", "programming", "technology"]):
        if is_arabic:
            return f"""🤖 سؤال تقني رائع!

{user_message}

في Flexible Soft، نتخصص في تطوير حلول الذكاء الاصطناعي والتقنيات الحديثة للشركات.

هل تريد مناقشة مشروع تقني؟ 💻

📧 <EMAIL>"""
        else:
            return f"""🤖 Great tech question!

At Flexible Soft, we specialize in AI solutions and modern technologies for businesses.

Want to discuss a tech project? 💻

📧 <EMAIL>"""

    # How-to questions
    elif any(word in msg for word in ["how to", "how do", "want to do", "want to make", "cook", "pasta"]):
        if is_arabic:
            return f"""👨‍🍳 سؤال عملي!

مثل الطبخ، تطوير التطبيقات يحتاج وصفة دقيقة! في Flexible Soft نطور تطبيقات للمطاعم والتوصيل.

هل تحتاج تطبيق لعملك؟ 📱

📧 <EMAIL>"""
        else:
            return f"""👨‍🍳 Practical question!

Like cooking, app development needs the right recipe! At Flexible Soft, we build restaurant and delivery apps.

Need an app for your business? 📱

📧 <EMAIL>"""

    # Business questions
    elif any(word in msg for word in ["business", "store", "shop", "company", "startup", "i have a", "i own"]):
        if is_arabic:
            return f"""💼 استشارة تجارية

يمكننا مساعدتك في تطوير حلول رقمية لعملك:
• موقع إلكتروني احترافي
• تطبيق جوال للعملاء
• نظام إدارة المبيعات
• حلول الدفع الإلكتروني

استشارة مجانية متاحة! 🚀

📧 <EMAIL>"""
        else:
            return f"""💼 Business consultation

We can help develop digital solutions for your business:
• Professional website
• Customer mobile app
• Sales management system
• E-commerce & payment solutions

Free consultation available! 🚀

📧 <EMAIL>"""

    # Jokes
    elif any(word in msg for word in ["joke", "funny", "humor", "laugh"]):
        if is_arabic:
            return """😄 نكتة تقنية!

لماذا يحب المبرمجون القهوة؟
لأنهم يحتاجون Java! ☕

في Flexible Soft، نحول الأخطاء إلى ميزات! 🐛➡️✨

📧 <EMAIL>"""
        else:
            return """😄 Tech joke!

Why do programmers prefer dark mode?
Because light attracts bugs! 🐛

At Flexible Soft, we turn bugs into features! 🐛➡️✨

📧 <EMAIL>"""

    # General response
    else:
        if is_arabic:
            return f"""💭 سؤال مثير للاهتمام!

في Flexible Soft، نؤمن أن التكنولوجيا يجب أن تحل المشاكل الحقيقية.

كيف يمكننا مساعدتك تقنياً؟ 🚀

📧 <EMAIL>"""
        else:
            return f"""💭 Interesting question!

At Flexible Soft, we believe technology should solve real problems.

How can we help you technically? 🚀

📧 <EMAIL>"""

# Legacy template functions (kept for reference but not used)
def handle_knowledge_question_legacy(user_message: str, is_arabic: bool) -> str:
    """Handle knowledge-based questions intelligently"""
    msg = user_message.lower()

    if "ai" in msg or "artificial intelligence" in msg:
        if is_arabic:
            return """🤖 **الذكاء الاصطناعي (AI)**

الذكاء الاصطناعي هو تقنية تمكن الآلات من محاكاة الذكاء البشري والتعلم من البيانات.

**التطبيقات:**
• المساعدات الذكية (مثل هذا الشات بوت)
• التعرف على الصور والصوت
• السيارات ذاتية القيادة
• التشخيص الطبي

**في Flexible Soft:**
نطور حلول ذكية مخصصة للشركات في السعودية والشرق الأوسط، مثل:
• شات بوت ذكي للعملاء
• أنظمة التوصية
• تحليل البيانات التجارية

هل تريد معرفة كيف يمكن للذكاء الاصطناعي أن يساعد عملك؟ 🚀"""
        else:
            return """🤖 **Artificial Intelligence (AI)**

AI is technology that enables machines to simulate human intelligence and learn from data.

**Key Applications:**
• Intelligent assistants (like this chatbot)
• Image and speech recognition
• Autonomous vehicles
• Medical diagnosis

**At Flexible Soft:**
We develop custom AI solutions for businesses in Saudi Arabia and the Middle East:
• Smart customer chatbots
• Recommendation systems
• Business data analytics
• Process automation

Want to know how AI can transform your business? 🚀

📧 <EMAIL>"""

    elif "python" in msg:
        if is_arabic:
            return """🐍 **لغة البرمجة Python**

Python هي لغة برمجة قوية ومرنة، مثالية للمبتدئين والخبراء.

**الاستخدامات:**
• تطوير المواقع (Django, FastAPI)
• الذكاء الاصطناعي وتعلم الآلة
• تحليل البيانات
• الأتمتة والسكريبتات

**في Flexible Soft:**
نستخدم Python لبناء:
• أنظمة الباك إند القوية
• حلول الذكاء الاصطناعي
• أدوات تحليل البيانات

هل تحتاج مساعدة في مشروع Python؟ 💻"""
        else:
            return """🐍 **Python Programming Language**

Python is a powerful, versatile programming language perfect for beginners and experts.

**Common Uses:**
• Web development (Django, FastAPI)
• AI and machine learning
• Data analysis and science
• Automation and scripting

**At Flexible Soft:**
We use Python to build:
• Robust backend systems
• AI-powered solutions
• Data analytics tools
• Business automation

Need help with a Python project? 💻

📧 <EMAIL>"""

    else:
        # Generic knowledge response
        if is_arabic:
            return f"""🧠 **سؤال ممتاز!**

سؤالك: "{user_message}"

أقدر فضولك! هذا النوع من الأسئلة يتطلب بحث عميق وتحليل دقيق.

**يمكنني مساعدتك في:**
• تطوير حلول تقنية مخصصة
• بناء أنظمة ذكية لعملك
• استشارات تقنية متخصصة

**Flexible Soft** متخصصة في تحويل الأفكار إلى حلول رقمية مبتكرة.

هل لديك مشروع تقني تريد تطويره؟ 🚀"""
        else:
            return f"""🧠 **Great Question!**

Your question: "{user_message}"

I appreciate your curiosity! This type of question requires deep research and analysis.

**I can help you with:**
• Custom technology solutions
• Building intelligent systems for your business
• Specialized tech consulting

**Flexible Soft** specializes in turning ideas into innovative digital solutions.

Do you have a tech project you'd like to develop? 🚀

📧 <EMAIL>"""

def handle_how_to_question(user_message: str, is_arabic: bool) -> str:
    """Handle how-to questions with practical advice"""
    msg = user_message.lower()

    if "cook" in msg or "pasta" in msg or "food" in msg:
        if is_arabic:
            return """👨‍🍳 **طبخ المعكرونة - دليل سريع**

**المكونات:**
• معكرونة (100 جرام لكل شخص)
• ملح
• زيت زيتون
• صلصة حسب الرغبة

**الطريقة:**
1. اغلي الماء مع الملح
2. أضف المعكرونة واتركها 8-12 دقيقة
3. صفي المعكرونة
4. أضف الصلصة والتوابل

**نصيحة تقنية من Flexible Soft:**
مثل الطبخ، تطوير التطبيقات يحتاج وصفة دقيقة! نحن نطور تطبيقات الطعام والتوصيل للمطاعم.

هل تريد تطبيق لمطعمك؟ 🍝📱"""
        else:
            return """👨‍🍳 **How to Cook Pasta - Quick Guide**

**Ingredients:**
• Pasta (100g per person)
• Salt
• Olive oil
• Sauce of choice

**Steps:**
1. Boil water with salt
2. Add pasta, cook 8-12 minutes
3. Drain the pasta
4. Add sauce and seasonings

**Tech Tip from Flexible Soft:**
Like cooking, app development needs the right recipe! We build food delivery and restaurant apps.

Want an app for your restaurant? 🍝📱

📧 <EMAIL>"""

    else:
        if is_arabic:
            return f"""🛠️ **دليل عملي**

سؤالك: "{user_message}"

أحب الأسئلة العملية! النجاح يأتي من التطبيق العملي.

**خطوات عامة للنجاح:**
1. حدد الهدف بوضوح
2. اجمع المعلومات اللازمة
3. ضع خطة عمل
4. نفذ خطوة بخطوة
5. قيم النتائج وطور

**Flexible Soft** تساعدك في تحويل أفكارك إلى حلول رقمية عملية.

هل تحتاج مساعدة تقنية لتحقيق هدفك؟ 🎯"""
        else:
            return f"""�️ **Practical Guide**

Your question: "{user_message}"

I love practical questions! Success comes from hands-on implementation.

**General Success Steps:**
1. Define your goal clearly
2. Gather necessary information
3. Create an action plan
4. Execute step by step
5. Evaluate and improve

**Flexible Soft** helps you turn ideas into practical digital solutions.

Need technical help to achieve your goal? 🎯

📧 <EMAIL>"""

def handle_joke_request(user_message: str, is_arabic: bool) -> str:
    """Handle joke requests with tech humor"""
    if is_arabic:
        return """😄 **نكتة تقنية!**

لماذا يحب المبرمجون القهوة؟
لأنهم يحتاجون Java لتشغيل كودهم! ☕

**نكتة أخرى:**
ما الفرق بين المبرمج والساحر؟
الساحر يخرج الأرانب من القبعة، والمبرمج يخرج الأخطاء من العدم! 🐰

**في Flexible Soft:**
نحول الأخطاء إلى ميزات، والمشاكل إلى حلول مبتكرة!

هل تريد تطبيق يجعل عملاءك يضحكون من السعادة؟ 😊🚀"""
    else:
        return """😄 **Tech Joke Time!**

Why do programmers prefer dark mode?
Because light attracts bugs! 🐛

**Another one:**
Why did the programmer quit his job?
He didn't get arrays! (a raise) 💰

**At Flexible Soft:**
We turn bugs into features and problems into innovative solutions!

Want an app that makes your customers smile with joy? 😊🚀

📧 <EMAIL>"""

def handle_business_question(user_message: str, is_arabic: bool) -> str:
    """Handle business-related questions"""
    if is_arabic:
        return f"""💼 **استشارة تجارية ذكية**

سؤالك: "{user_message}"

**حلول رقمية لعملك:**
• موقع إلكتروني احترافي
• تطبيق جوال للعملاء
• نظام إدارة المبيعات
• شات بوت ذكي للدعم
• حلول الدفع الإلكتروني

**نجاحات Flexible Soft:**
✅ 50+ مشروع ناجح
✅ عملاء في السعودية والخليج
✅ تقنيات حديثة (AI, React, Flutter)

**استشارة مجانية:**
احجز جلسة لمناقشة مشروعك وكيف يمكن للتكنولوجيا أن تنمي عملك.

📧 <EMAIL>
🌐 https://sofflex-website.dev.flexible-soft.com/"""
    else:
        return f"""💼 **Smart Business Consultation**

Your question: "{user_message}"

**Digital Solutions for Your Business:**
• Professional website development
• Mobile app for customers
• Sales management system
• AI-powered customer support
• E-commerce and payment solutions

**Flexible Soft Success:**
✅ 50+ successful projects
✅ Clients across Saudi Arabia & Gulf
✅ Modern tech stack (AI, React, Flutter)

**Free Consultation:**
Book a session to discuss your project and how technology can grow your business.

📧 <EMAIL>
🌐 https://sofflex-website.dev.flexible-soft.com/"""

def handle_company_question(user_message: str, is_arabic: bool) -> str:
    """Handle questions about Flexible Soft"""
    if is_arabic:
        return """🏢 **Flexible Soft (Sofflex)**

**من نحن:**
شركة تطوير برمجيات متخصصة في الحلول الذكية للشركات في السعودية والشرق الأوسط.

**خدماتنا:**
🌐 تطوير المواقع (Next.js, React)
📱 تطبيقات الجوال (Flutter, React Native)
🤖 حلول الذكاء الاصطناعي
⚡ أتمتة العمليات التجارية
🛒 منصات التجارة الإلكترونية

**تقنياتنا:**
• Frontend: Next.js, React, TypeScript
• Backend: Python, FastAPI, Node.js
• Mobile: Flutter, React Native
• AI: Machine Learning, NLP, Computer Vision

**لماذا نحن مختلفون:**
✨ فهم عميق للسوق السعودي
✨ دعم باللغة العربية
✨ حلول مخصصة لكل عميل
✨ فريق خبير ومتفاني

📧 <EMAIL>"""
    else:
        return """🏢 **Flexible Soft (Sofflex)**

**Who We Are:**
A software development company specializing in intelligent solutions for businesses in Saudi Arabia and the Middle East.

**Our Services:**
🌐 Web Development (Next.js, React)
📱 Mobile Apps (Flutter, React Native)
🤖 AI Solutions & Automation
⚡ Business Process Automation
🛒 E-commerce Platforms

**Our Tech Stack:**
• Frontend: Next.js, React, TypeScript
• Backend: Python, FastAPI, Node.js
• Mobile: Flutter, React Native
• AI: Machine Learning, NLP, Computer Vision

**Why Choose Us:**
✨ Deep understanding of Saudi market
✨ Arabic language support
✨ Custom solutions for each client
✨ Expert and dedicated team

📧 <EMAIL>
🌐 https://sofflex-website.dev.flexible-soft.com/"""

def handle_general_question(user_message: str, is_arabic: bool) -> str:
    """Handle general questions with intelligent responses"""
    if is_arabic:
        return f"""💭 **سؤال مثير للاهتمام!**

"{user_message}"

أقدر تفكيرك العميق! كل سؤال يفتح باب للتعلم والاكتشاف.

**فلسفة Flexible Soft:**
نؤمن أن التكنولوجيا يجب أن تحل المشاكل الحقيقية وتحسن حياة الناس.

**كيف يمكننا مساعدتك:**
• تحويل أفكارك إلى تطبيقات عملية
• بناء حلول تقنية مبتكرة
• استشارات تقنية متخصصة

هل لديك فكرة تريد تطويرها؟ دعنا نناقشها! 🚀

📧 <EMAIL>"""
    else:
        return f"""💭 **Interesting Question!**

"{user_message}"

I appreciate your thoughtful inquiry! Every question opens doors to learning and discovery.

**Flexible Soft Philosophy:**
We believe technology should solve real problems and improve people's lives.

**How We Can Help:**
• Turn your ideas into practical applications
• Build innovative tech solutions
• Provide specialized technical consulting

Have an idea you'd like to develop? Let's discuss it! 🚀

📧 <EMAIL>
🌐 https://sofflex-website.dev.flexible-soft.com/"""

@app.on_event("startup")
async def startup_event():
    """Load LLaMA model on startup"""
    print("🚀 Starting FS Assistant with LLaMA integration...")
    success = load_llama_model()
    if success:
        print("✅ LLaMA model loaded successfully!")
    else:
        print("⚠️ Running in fallback mode - install transformers and get LLaMA access for full AI features")

@app.get("/")
async def root():
    return {
        "message": "FS - Flexible Soft AI Assistant (LLaMA-Powered)", 
        "status": "running",
        "model_loaded": model is not None
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy", 
        "timestamp": datetime.now(),
        "model_status": "loaded" if model is not None else "fallback_mode"
    }

@app.post("/api/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """LLaMA-powered intelligent conversation endpoint"""
    
    # Generate session ID if not provided
    session_id = request.session_id or str(uuid.uuid4())
    
    # Get intelligent response from LLaMA
    response_message = await get_llama_response(request.message, session_id)
    
    return ChatResponse(
        message=response_message,
        session_id=session_id,
        timestamp=datetime.now(),
        sources=["FS - Flexible Soft AI Assistant (LLaMA-Powered)"]
    )

if __name__ == "__main__":
    uvicorn.run(
        "app.main_llama:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=False  # Disable reload to prevent model reloading
    )

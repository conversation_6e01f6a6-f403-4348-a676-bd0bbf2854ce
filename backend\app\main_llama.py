"""
FS - Flexible Soft AI Assistant
Enhanced LLaMA-powered intelligent conversation system with dynamic reasoning
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, List, Dict, AsyncGenerator
from datetime import datetime
import uuid
import uvicorn
import torch
import logging
import asyncio
import json
from threading import Lock

# Import settings
from .config_simple import settings

app = FastAPI(
    title="FS - Flexible Soft AI Assistant (Enhanced LLaMA-Powered)",
    description="Truly intelligent AI assistant powered by LLaMA models with dynamic reasoning",
    version="6.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    language: Optional[str] = "en"

class ChatResponse(BaseModel):
    message: str
    session_id: str
    timestamp: datetime
    sources: Optional[List[str]] = None

# Global variables for model
model = None
tokenizer = None
model_lock = Lock()
current_model_name = None
conversation_history: Dict[str, List[Dict]] = {}

# Enhanced System Prompt for Dynamic Reasoning and Intelligence
SYSTEM_PROMPT = """You are FS, the Flexible Soft AI Assistant.

You represent Flexible Soft (Sofflex) — a modern IT company that builds web and mobile applications, AI solutions, and digital transformation systems for clients worldwide.

💡 Behavior Guidelines:
- Respond naturally, like a friendly human expert — not robotic.
- Answer **ANY** user question (inside or outside the company domain).
- When questions are general (e.g., "How to cook pasta?"), respond intelligently and optionally relate to technology or Flexible Soft's expertise.
- If the question is about Flexible Soft, give accurate company info.
- If the user input has grammar or spelling mistakes, fix and understand it silently.
- Never repeat the user's question or say "interesting question".
- Automatically detect and respond in Arabic or English.
- Be professional but warm, smart, and engaging.
- Encourage users to explore projects and digital ideas with Flexible Soft when relevant.

🏢 Company Information:
- Flexible Soft (Sofflex): Leading IT company in Saudi Arabia & Middle East
- Services: Web Development (Next.js, React, Typo3), Mobile App Development (Flutter), AI & Machine Learning Solutions, E-commerce Platforms, Cloud & DevOps, Technical Consulting and Outsourcing
- Tech Stack: Next.js, React, TypeScript, Python, FastAPI, Flutter, AI/ML, databases, cloud technologies
- Contact: <EMAIL> | Website: https://sofflex-website.dev.flexible-soft.com/
- Specializes in custom solutions for Saudi market with Arabic language support

🎯 Dynamic Response Strategy:
1. **Company-Specific Questions**: Provide detailed, accurate information about Flexible Soft services, expertise, and solutions
2. **Project-Related Questions**: Offer practical advice and suggest how Flexible Soft can help implement solutions
3. **General Questions**: Answer intelligently and naturally relate to technology when appropriate
4. **Casual Greetings**: Respond warmly and invite conversation about projects or needs
5. **Design/Technical Questions**: Provide expert advice and showcase Flexible Soft's capabilities

🌍 Language Support:
- Automatically detect Arabic or English
- Respond in the same language as the user
- Handle mixed-language inputs gracefully
- Support casual greetings like 'hii', 'hello', 'مرحبا'

💬 Conversation Style:
- Be conversational and natural, not templated
- Use appropriate emojis sparingly
- Provide actionable suggestions
- Avoid generic fallback responses
- Show enthusiasm for technology and innovation
- Maintain professional yet friendly tone"""

def load_llama_model():
    """Load LLaMA model and tokenizer with enhanced error handling"""
    global model, tokenizer, current_model_name
    
    try:
        from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
        import torch
        
        # Model selection (try open models first, then LLaMA if available)
        model_options = [
            "microsoft/DialoGPT-medium",  # Smaller, faster model
            "microsoft/DialoGPT-large",
            "microsoft/DialoGPT-small",
            "meta-llama/Llama-3-8b-chat-hf",
            "meta-llama/Llama-2-13b-chat-hf",
            "meta-llama/Llama-2-7b-chat-hf"
        ]
        
        for model_name in model_options:
            try:
                print(f"🔄 Attempting to load model: {model_name}")
                
                # Load tokenizer
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                if tokenizer.pad_token is None:
                    tokenizer.pad_token = tokenizer.eos_token
                
                # Configure model loading
                if torch.cuda.is_available():
                    print("🚀 CUDA available - loading model on GPU")
                    model = AutoModelForCausalLM.from_pretrained(
                        model_name,
                        torch_dtype=torch.float16,
                        device_map="auto",
                        trust_remote_code=True
                    )
                else:
                    print("💻 Loading model on CPU")
                    model = AutoModelForCausalLM.from_pretrained(
                        model_name,
                        torch_dtype=torch.float32,
                        trust_remote_code=True
                    )
                
                current_model_name = model_name
                print(f"✅ Successfully loaded model: {model_name}")
                return True
                
            except Exception as e:
                print(f"❌ Failed to load {model_name}: {e}")
                continue
        
        if model is None:
            raise Exception("Failed to load any LLaMA model")
            
        return True
        
    except Exception as e:
        print(f"❌ Error loading LLaMA model: {e}")
        print("💡 Make sure you have:")
        print("   - pip install transformers torch accelerate bitsandbytes")
        print("   - Hugging Face access to LLaMA models")
        print("   - Sufficient GPU memory or use CPU fallback")
        return False

def categorize_question(user_message: str) -> str:
    """Categorize user question for dynamic response strategy"""
    msg = user_message.lower().strip()
    
    # Company-specific patterns
    company_patterns = [
        "sofflex", "flexible soft", "your company", "services", "what u do", 
        "what you do", "what do you do", "about you", "who are you",
        "contact", "email", "website", "portfolio", "projects"
    ]
    
    # Project-related patterns
    project_patterns = [
        "build", "develop", "create", "make", "design", "website", "app", 
        "mobile", "system", "platform", "business", "store", "shop", 
        "startup", "i have a", "i own", "need help", "consultation"
    ]
    
    # Technical patterns
    technical_patterns = [
        "ai", "artificial intelligence", "python", "programming", "technology",
        "react", "next.js", "flutter", "database", "api", "backend", "frontend"
    ]
    
    # Casual greeting patterns
    greeting_patterns = [
        "hi", "hii", "hello", "hey", "مرحبا", "السلام عليكم", "أهلا", "هلا"
    ]
    
    if any(pattern in msg for pattern in company_patterns):
        return "company"
    elif any(pattern in msg for pattern in project_patterns):
        return "project"
    elif any(pattern in msg for pattern in technical_patterns):
        return "technical"
    elif any(pattern in msg for pattern in greeting_patterns):
        return "greeting"
    else:
        return "general"

def detect_arabic(text: str) -> bool:
    """Detect if text contains Arabic characters"""
    arabic_chars = ["ا", "ب", "ت", "ث", "ج", "ح", "خ", "د", "ذ", "ر", "ز", "س", "ش", "ص", "ض", "ط", "ظ", "ع", "غ", "ف", "ق", "ك", "ل", "م", "ن", "ه", "و", "ي"]
    return any(char in text for char in arabic_chars)

async def get_llama_response(user_message: str, session_id: str) -> str:
    """Get intelligent response from AI model with enhanced conversation handling"""
    global model, tokenizer, conversation_history

    if model is None or tokenizer is None:
        return get_dynamic_fallback_response(user_message)

    try:
        # Get or initialize conversation history
        if session_id not in conversation_history:
            conversation_history[session_id] = []

        # Create clean message format for AI model
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT}
        ]

        # Add recent conversation history (last 6 messages to stay within token limits)
        recent_messages = conversation_history[session_id][-6:] if len(conversation_history[session_id]) > 6 else conversation_history[session_id]
        messages.extend(recent_messages)

        # Add current user message
        messages.append({"role": "user", "content": user_message})

        # Format conversation for the model
        if current_model_name and "dialogpt" in current_model_name.lower():
            # DialoGPT format
            conversation_text = f"FS Assistant: I'm FS from Flexible Soft, ready to help with any questions!<|endoftext|>"
            
            for msg in messages[-4:]:  # Last 4 messages for context
                if msg["role"] == "user":
                    conversation_text += f"User: {msg['content']}<|endoftext|>"
                elif msg["role"] == "assistant":
                    conversation_text += f"FS Assistant: {msg['content']}<|endoftext|>"
            
            conversation_text += f"User: {user_message}<|endoftext|>FS Assistant:"
            
            inputs = tokenizer.encode(conversation_text, return_tensors="pt")
            if torch.cuda.is_available():
                inputs = inputs.to("cuda")

            with model_lock:
                with torch.no_grad():
                    outputs = model.generate(
                        inputs,
                        max_new_tokens=200,
                        temperature=0.7,
                        do_sample=True,
                        top_p=0.9,
                        repetition_penalty=1.1,
                        pad_token_id=tokenizer.eos_token_id,
                        eos_token_id=tokenizer.eos_token_id
                    )

            response = tokenizer.decode(outputs[0][inputs.shape[-1]:], skip_special_tokens=True)
            response = response.replace("<|endoftext|>", "").strip()

        else:
            # LLaMA format
            formatted_conversation = format_llama_conversation(messages)
            
            inputs = tokenizer(
                formatted_conversation,
                return_tensors="pt",
                truncation=True,
                max_length=4000,
                padding=True
            )

            if torch.cuda.is_available():
                inputs = {k: v.to("cuda") for k, v in inputs.items()}

            with model_lock:
                with torch.no_grad():
                    outputs = model.generate(
                        **inputs,
                        max_new_tokens=300,
                        temperature=0.7,
                        do_sample=True,
                        top_p=0.9,
                        repetition_penalty=1.1,
                        pad_token_id=tokenizer.eos_token_id,
                        eos_token_id=tokenizer.eos_token_id
                    )

            full_response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = full_response.split("Assistant:")[-1].strip()

        # Clean and validate response
        if response and len(response) > 3 and response != user_message and not response.startswith("You are FS"):
            # Update conversation history
            conversation_history[session_id].append({"role": "user", "content": user_message})
            conversation_history[session_id].append({"role": "assistant", "content": response})

            # Limit conversation history
            if len(conversation_history[session_id]) > 20:
                conversation_history[session_id] = conversation_history[session_id][-20:]

            return response
        else:
            # Fallback to dynamic response if AI output is poor
            print(f"⚠️ AI response validation failed: '{response}' - using dynamic fallback")
            return get_dynamic_fallback_response(user_message)

    except Exception as e:
        print(f"❌ Error generating AI response: {e}")
        return get_dynamic_fallback_response(user_message)

def format_llama_conversation(messages: List[Dict]) -> str:
    """Format conversation for LLaMA models"""
    conversation = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n{SYSTEM_PROMPT}<|eot_id|>\n"

    for msg in messages[1:]:  # Skip system message as it's already added
        role = msg["role"]
        content = msg["content"]
        conversation += f"<|start_header_id|>{role}<|end_header_id|>\n{content}<|eot_id|>\n"

    conversation += "<|start_header_id|>assistant<|end_header_id|>\n"
    return conversation

def get_dynamic_fallback_response(user_message: str) -> str:
    """Dynamic intelligent response based on question categorization when AI model is unavailable"""
    is_arabic = detect_arabic(user_message)
    category = categorize_question(user_message)

    if category == "company":
        return get_company_response(is_arabic)
    elif category == "project":
        return get_project_response(user_message, is_arabic)
    elif category == "technical":
        return get_technical_response(user_message, is_arabic)
    elif category == "greeting":
        return get_greeting_response(is_arabic)
    else:
        return get_general_response(user_message, is_arabic)

def get_company_response(is_arabic: bool) -> str:
    """Response for company-specific questions"""
    if is_arabic:
        return """🏢 **Flexible Soft (سوفليكس)**

نحن شركة تطوير برمجيات رائدة متخصصة في:
• تطوير المواقع (Next.js, React, Typo3)
• تطبيقات الجوال (Flutter)
• حلول الذكاء الاصطناعي والتعلم الآلي
• منصات التجارة الإلكترونية
• الحوسبة السحابية و DevOps
• الاستشارات التقنية والاستعانة بالمصادر الخارجية

نخدم السوق السعودي والشرق الأوسط بحلول تقنية مبتكرة ومخصصة.

📧 <EMAIL>
🌐 https://sofflex-website.dev.flexible-soft.com/"""
    else:
        return """🏢 **Flexible Soft (Sofflex)**

We're a leading IT company specializing in:
• Web Development (Next.js, React, Typo3)
• Mobile App Development (Flutter)
• AI & Machine Learning Solutions
• E-commerce Platforms
• Cloud & DevOps
• Technical Consulting and Outsourcing

We serve Saudi Arabia & Middle East with innovative, custom tech solutions.

📧 <EMAIL>
🌐 https://sofflex-website.dev.flexible-soft.com/"""

def get_greeting_response(is_arabic: bool) -> str:
    """Response for casual greetings"""
    if is_arabic:
        return """👋 أهلاً وسهلاً!

أنا FS، مساعد Flexible Soft الذكي. يمكنني مساعدتك في:
• أسئلة تقنية وبرمجية
• استشارات تطوير المشاريع
• معلومات عن خدماتنا
• أي موضوع عام تريد مناقشته

كيف يمكنني مساعدتك اليوم؟ 😊

📧 <EMAIL>"""
    else:
        return """👋 Hello there!

I'm FS, Flexible Soft's intelligent assistant. I can help you with:
• Technical and programming questions
• Project development consultations
• Information about our services
• Any general topic you'd like to discuss

How can I help you today? 😊

📧 <EMAIL>"""

def get_technical_response(user_message: str, is_arabic: bool) -> str:
    """Response for technical questions"""
    if is_arabic:
        return f"""🤖 سؤال تقني ممتاز!

في Flexible Soft، نتخصص في التقنيات الحديثة مثل:
• الذكاء الاصطناعي والتعلم الآلي
• تطوير الويب (Next.js, React, Python)
• تطبيقات الجوال (Flutter)
• الحوسبة السحابية والأتمتة

هل تريد مناقشة مشروع تقني محدد؟ 💻

📧 <EMAIL>"""
    else:
        return f"""🤖 Excellent technical question!

At Flexible Soft, we specialize in modern technologies like:
• AI & Machine Learning
• Web development (Next.js, React, Python)
• Mobile apps (Flutter)
• Cloud computing & automation

Want to discuss a specific tech project? 💻

📧 <EMAIL>"""

def get_project_response(user_message: str, is_arabic: bool) -> str:
    """Response for project-related questions"""
    if is_arabic:
        return f"""💼 رائع! دعنا نناقش مشروعك

يمكننا مساعدتك في تطوير:
• مواقع إلكترونية احترافية
• تطبيقات جوال للعملاء
• أنظمة إدارة الأعمال
• منصات التجارة الإلكترونية
• حلول الذكاء الاصطناعي

استشارة مجانية لمناقشة فكرتك! 🚀

📧 <EMAIL>"""
    else:
        return f"""💼 Great! Let's discuss your project

We can help you develop:
• Professional websites
• Customer mobile apps
• Business management systems
• E-commerce platforms
• AI-powered solutions

Free consultation to discuss your idea! 🚀

📧 <EMAIL>"""

def get_general_response(user_message: str, is_arabic: bool) -> str:
    """Response for general questions"""
    if is_arabic:
        return f"""💭 سؤال مثير للاهتمام!

في Flexible Soft، نؤمن أن التكنولوجيا يجب أن تحل المشاكل الحقيقية وتحسن حياة الناس.

سواء كان سؤالك عن التقنية أو الأعمال أو أي موضوع آخر، أنا هنا للمساعدة!

هل لديك مشروع أو فكرة تريد تطويرها؟ 🚀

📧 <EMAIL>"""
    else:
        return f"""💭 Interesting question!

At Flexible Soft, we believe technology should solve real problems and improve people's lives.

Whether your question is about tech, business, or any other topic, I'm here to help!

Do you have a project or idea you'd like to develop? 🚀

📧 <EMAIL>"""

# FastAPI Application Routes

@app.on_event("startup")
async def startup_event():
    """Load LLaMA model on startup"""
    print("🚀 Starting FS Assistant with enhanced LLaMA integration...")
    success = load_llama_model()
    if success:
        print("✅ LLaMA model loaded successfully!")
    else:
        print("⚠️ Running in dynamic fallback mode - install transformers and get LLaMA access for full AI features")

@app.get("/")
async def root():
    return {
        "message": "FS - Flexible Soft AI Assistant (Enhanced LLaMA-Powered)",
        "status": "running",
        "model_loaded": model is not None,
        "version": "6.0.0"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "model_status": "loaded" if model is not None else "dynamic_fallback_mode",
        "conversation_sessions": len(conversation_history)
    }

@app.post("/api/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Enhanced LLaMA-powered intelligent conversation endpoint with dynamic reasoning"""

    # Generate session ID if not provided
    session_id = request.session_id or str(uuid.uuid4())

    # Get intelligent response from LLaMA or dynamic fallback
    response_message = await get_llama_response(request.message, session_id)

    return ChatResponse(
        message=response_message,
        session_id=session_id,
        timestamp=datetime.now(),
        sources=["FS - Flexible Soft AI Assistant (Enhanced LLaMA-Powered)"]
    )

@app.post("/api/chat/stream")
async def chat_stream(request: ChatRequest):
    """Streaming version of the chat endpoint for real-time responses"""

    # Generate session ID if not provided
    session_id = request.session_id or str(uuid.uuid4())

    async def generate_stream():
        """Generate streaming response"""
        try:
            # For streaming, we'll simulate token-by-token generation
            response_message = await get_llama_response(request.message, session_id)

            # Split response into words for streaming effect
            words = response_message.split()

            # Send initial metadata
            yield f"data: {json.dumps({'type': 'start', 'session_id': session_id, 'timestamp': datetime.now().isoformat()})}\n\n"

            # Stream words with small delays
            current_text = ""
            for i, word in enumerate(words):
                current_text += word + " "
                chunk_data = {
                    'type': 'chunk',
                    'content': word + " ",
                    'full_content': current_text.strip(),
                    'is_final': i == len(words) - 1
                }
                yield f"data: {json.dumps(chunk_data)}\n\n"

                # Small delay for streaming effect
                await asyncio.sleep(0.05)

            # Send completion signal
            yield f"data: {json.dumps({'type': 'end', 'final_content': current_text.strip()})}\n\n"

        except Exception as e:
            error_data = {
                'type': 'error',
                'error': str(e),
                'fallback_response': get_dynamic_fallback_response(request.message)
            }
            yield f"data: {json.dumps(error_data)}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "app.main_llama:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=False  # Disable reload to prevent model reloading
    )

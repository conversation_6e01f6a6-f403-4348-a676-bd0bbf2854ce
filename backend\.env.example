# FS - Flexible Soft AI Assistant Configuration
# Enhanced environment configuration for LLaMA-powered chatbot

# =============================================================================
# API Configuration
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True
ENVIRONMENT=development

# =============================================================================
# CORS Settings
# =============================================================================
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,https://your-domain.com

# =============================================================================
# AI Model Configuration
# =============================================================================
# Hugging Face token for accessing LLaMA models (required for LLaMA models)
HUGGINGFACE_TOKEN=your_huggingface_token_here

# Primary model to use (enhanced LLaMA-3 recommended)
MODEL_NAME=meta-llama/Llama-3-8b-chat-hf
# Alternative models (fallback options)
FALLBACK_MODELS=microsoft/DialoGPT-medium,microsoft/DialoGPT-large,microsoft/DialoGPT-small

# Model generation parameters
MAX_TOKENS=300
TEMPERATURE=0.7
TOP_P=0.9
REPETITION_PENALTY=1.1

# =============================================================================
# Conversation Management
# =============================================================================
# Maximum conversation history to keep in memory
MAX_CONVERSATION_HISTORY=20
# Maximum messages to send to model for context
MAX_CONTEXT_MESSAGES=6

# =============================================================================
# Streaming Configuration
# =============================================================================
# Enable streaming responses
ENABLE_STREAMING=true
# Streaming chunk delay (seconds)
STREAMING_DELAY=0.05

# =============================================================================
# Company Branding
# =============================================================================
COMPANY_NAME=Flexible Soft
COMPANY_SHORT_NAME=Sofflex
COMPANY_DOMAIN=sofflex.com
COMPANY_EMAIL=<EMAIL>
COMPANY_WEBSITE=https://sofflex-website.dev.flexible-soft.com/
COMPANY_DESCRIPTION=A leading software, web, and AI development company

# =============================================================================
# Language Support
# =============================================================================
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,ar
ENABLE_AUTO_LANGUAGE_DETECTION=true

# =============================================================================
# Security
# =============================================================================
SECRET_KEY=your-secret-key-here-change-in-production-make-it-very-long-and-random
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# Rate Limiting
# =============================================================================
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# =============================================================================
# Logging
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE=logs/fs_assistant.log
ENABLE_REQUEST_LOGGING=true

# =============================================================================
# Performance
# =============================================================================
# Use GPU if available
USE_GPU=true
# Model precision (float16 for GPU, float32 for CPU)
MODEL_PRECISION=float16
# Enable model caching
ENABLE_MODEL_CACHE=true

# =============================================================================
# Development Settings
# =============================================================================
# Enable development features
ENABLE_DEV_MODE=true
# Enable API documentation
ENABLE_DOCS=true
# Enable CORS for development
ENABLE_CORS=true

# =============================================================================
# Legacy Configuration (for compatibility)
# =============================================================================
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
VECTOR_STORE_PATH=./vector_store
VECTOR_DIMENSION=384
TOP_K_RESULTS=5
DOCUMENTS_PATH=./data
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=pdf,docx,txt,md
DATABASE_URL=sqlite:///./chatyy.db

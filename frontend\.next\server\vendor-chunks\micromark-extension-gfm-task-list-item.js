"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-task-list-item";
exports.ids = ["vendor-chunks/micromark-extension-gfm-task-list-item"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItemHtml: () => (/* binding */ gfmTaskListItemHtml)\n/* harmony export */ });\n/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\n/**\n * Create an HTML extension for `micromark` to support GFM task list items when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM task list items when serializing to HTML.\n */\nfunction gfmTaskListItemHtml() {\n  return {\n    enter: {\n      taskListCheck() {\n        this.tag('<input type=\"checkbox\" disabled=\"\" ')\n      }\n    },\n    exit: {\n      taskListCheck() {\n        this.tag('/>')\n      },\n      taskListCheckValueChecked() {\n        this.tag('checked=\"\" ')\n      }\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tdGFzay1saXN0LWl0ZW0vZGV2L2xpYi9odG1sLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLFlBQVksZUFBZTtBQUMzQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZsZXhpYmxlLXNvZnQtY2hhdGJvdC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9taWNyb21hcmstZXh0ZW5zaW9uLWdmbS10YXNrLWxpc3QtaXRlbS9kZXYvbGliL2h0bWwuanM/MmM4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0h0bWxFeHRlbnNpb259IGZyb20gJ21pY3JvbWFyay11dGlsLXR5cGVzJ1xuICovXG5cbi8qKlxuICogQ3JlYXRlIGFuIEhUTUwgZXh0ZW5zaW9uIGZvciBgbWljcm9tYXJrYCB0byBzdXBwb3J0IEdGTSB0YXNrIGxpc3QgaXRlbXMgd2hlblxuICogc2VyaWFsaXppbmcgdG8gSFRNTC5cbiAqXG4gKiBAcmV0dXJucyB7SHRtbEV4dGVuc2lvbn1cbiAqICAgRXh0ZW5zaW9uIGZvciBgbWljcm9tYXJrYCB0aGF0IGNhbiBiZSBwYXNzZWQgaW4gYGh0bWxFeHRlbnNpb25zYCB0b1xuICogICBzdXBwb3J0IEdGTSB0YXNrIGxpc3QgaXRlbXMgd2hlbiBzZXJpYWxpemluZyB0byBIVE1MLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2ZtVGFza0xpc3RJdGVtSHRtbCgpIHtcbiAgcmV0dXJuIHtcbiAgICBlbnRlcjoge1xuICAgICAgdGFza0xpc3RDaGVjaygpIHtcbiAgICAgICAgdGhpcy50YWcoJzxpbnB1dCB0eXBlPVwiY2hlY2tib3hcIiBkaXNhYmxlZD1cIlwiICcpXG4gICAgICB9XG4gICAgfSxcbiAgICBleGl0OiB7XG4gICAgICB0YXNrTGlzdENoZWNrKCkge1xuICAgICAgICB0aGlzLnRhZygnLz4nKVxuICAgICAgfSxcbiAgICAgIHRhc2tMaXN0Q2hlY2tWYWx1ZUNoZWNrZWQoKSB7XG4gICAgICAgIHRoaXMudGFnKCdjaGVja2VkPVwiXCIgJylcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItem: () => (/* binding */ gfmTaskListItem)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/**\n * @import {Extension, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n\n\n\nconst tasklistCheck = {name: 'tasklistCheck', tokenize: tokenizeTasklistCheck}\n\n/**\n * Create an HTML extension for `micromark` to support GFM task list items\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM task list items when serializing to HTML.\n */\nfunction gfmTaskListItem() {\n  return {\n    text: {[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: tasklistCheck}\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTasklistCheck(effects, ok, nok) {\n  const self = this\n\n  return open\n\n  /**\n   * At start of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, 'expected `[`')\n\n    if (\n      // Exit if there’s stuff before.\n      self.previous !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      // Exit if not in the first content that is the first child of a list\n      // item.\n      !self._gfmTasklistFirstContentOfListItem\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('taskListCheck')\n    effects.enter('taskListCheckMarker')\n    effects.consume(code)\n    effects.exit('taskListCheckMarker')\n    return inside\n  }\n\n  /**\n   * In task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // Currently we match how GH works in files.\n    // To match how GH works in comments, use `markdownSpace` (`[\\t ]`) instead\n    // of `markdownLineEndingOrSpace` (`[\\t\\n\\r ]`).\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n      effects.enter('taskListCheckValueUnchecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueUnchecked')\n      return close\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseX || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseX) {\n      effects.enter('taskListCheckValueChecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueChecked')\n      return close\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * At close of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function close(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n      effects.enter('taskListCheckMarker')\n      effects.consume(code)\n      effects.exit('taskListCheckMarker')\n      effects.exit('taskListCheck')\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * @type {State}\n   */\n  function after(code) {\n    // EOL in paragraph means there must be something else after it.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      return ok(code)\n    }\n\n    // Space or tab?\n    // Check what comes after.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return effects.check({tokenize: spaceThenNonSpace}, ok, nok)(code)\n    }\n\n    // EOF, or non-whitespace, both wrong.\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction spaceThenNonSpace(effects, ok, nok) {\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, after, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.types.whitespace)\n\n  /**\n   * After whitespace, after task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // EOF means there was nothing, so bad.\n    // EOL means there’s content after it, so good.\n    // Impossible to have more spaces.\n    // Anything else is good.\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ? nok(code) : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js\n");

/***/ })

};
;